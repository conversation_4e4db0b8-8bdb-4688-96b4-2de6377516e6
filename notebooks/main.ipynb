

import boto3

# --- 1. <PERSON><PERSON> Credentials ---
AWS_ACCESS_KEY_ID = "YOUR_AWS_ACCESS_KEY_ID"
AWS_SECRET_ACCESS_KEY = "YOUR_AWS_SECRET_ACCESS_KEY"
AWS_REGION = "us-east-1"  # or whichever region your Bedrock is in

# --- 2. Initialize Bedrock Runtime Client ---
bedrock_runtime = boto3.client(
    service_name="bedrock-runtime",
    region_name="us-east-1",
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9"
)

# --- 3. Call the Model ---
model_id = "amazon.nova-lite-v1:0"

prompt = "Explain S3 bucket versioning in simple terms."

response = bedrock_runtime.invoke_model(
    modelId=model_id,
    contentType="application/json",
    accept="application/json",
    body=f'''{{
        "messages": [
            {{"role": "user", "content": [{{"text": "{prompt}"}}]}}
        ]
    }}'''
)

# --- 4. Print Output ---
import json
result = json.loads(response["body"].read())
print(json.dumps(result, indent=2))






import boto3
import time
import json
from pathlib import Path
import uuid

# Initialize AWS clients
s3_client = boto3.client('s3')
bda_runtime_client = boto3.client('bedrock', region_name='us-east-1')  # Adjust region as needed

# Configuration
bucket_name = 'your-s3-bucket-name'  # Replace with your S3 bucket name
input_key = 'invoices/sample_invoice.pdf'  # Path to invoice in S3
output_key = 'bda_output/'  # Output directory in S3
project_arn = 'arn:aws:bedrock:us-west-2:YOUR_ACCOUNT_ID:data-automation-project/YOUR_PROJECT_ID'  # Replace with your BDA project ARN
data_automation_profile_arn = 'arn:aws:bedrock:us-west-2:YOUR_ACCOUNT_ID:data-automation-profile/us.data-automation-v1'  # Replace with your profile ARN

def upload_invoice_to_s3(local_file_path):
    """Upload invoice file to S3."""
    try:
        s3_client.upload_file(local_file_path, bucket_name, input_key)
        print(f"Uploaded invoice to s3://{bucket_name}/{input_key}")
        return f"s3://{bucket_name}/{input_key}"
    except Exception as e:
        print(f"Error uploading file to S3: {e}")
        raise

def invoke_bda_job(document_s3_uri):
    """Invoke Bedrock Data Automation async job."""
    try:
        response = bda_runtime_client.invoke_data_automation_async(
            inputConfiguration={'s3Uri': document_s3_uri},
            outputConfiguration={'s3Uri': f"s3://{bucket_name}/{output_key}"},
            dataAutomationConfiguration={
                'dataAutomationProjectArn': project_arn,
                'stage': 'LIVE'
            },
            dataAutomationProfileArn=data_automation_profile_arn
        )
        invocation_arn = response['invocationArn']
        print(f"Invoked BDA job with ARN: {invocation_arn}")
        return invocation_arn
    except Exception as e:
        print(f"Error invoking BDA job: {e}")
        raise

def check_job_status(invocation_arn):
    """Check the status of the BDA job."""
    while True:
        try:
            response = bda_runtime_client.get_data_automation_status(
                invocationArn=invocation_arn
            )
            status = response['status']
            print(f"Job status: {status}")
            if status in ['Success', 'Failed']:
                return status, response.get('outputLocation', {})
            time.sleep(10)  # Wait before polling again
        except Exception as e:
            print(f"Error checking job status: {e}")
            raise

def retrieve_results(output_s3_uri):
    """Retrieve and display BDA job results from S3."""
    try:
        bucket, key = output_s3_uri.replace("s3://", "").split("/", 1)
        response = s3_client.get_object(Bucket=bucket, Key=key)
        results = json.loads(response['Body'].read().decode('utf-8'))
        print("Extracted Invoice Data:")
        print(json.dumps(results, indent=2))
        return results
    except Exception as e:
        print(f"Error retrieving results: {e}")
        raise

def main():
    # Path to local invoice file (replace with your file path)
    local_invoice_path = 'sample_invoice.pdf'  # Ensure this file exists locally

    # Step 1: Upload invoice to S3
    document_s3_uri = upload_invoice_to_s3(local_invoice_path)

    # Step 2: Invoke BDA job
    invocation_arn = invoke_bda_job(document_s3_uri)

    # Step 3: Monitor job status
    status, output_location = check_job_status(invocation_arn)
    if status != 'Success':
        print("BDA job failed.")
        return

    # Step 4: Retrieve results
    output_s3_uri = output_location.get('s3Uri')
    if output_s3_uri:
        results = retrieve_results(output_s3_uri)
    else:
        print("No output location found.")

if __name__ == "__main__":
    main()







system_prompt = """You are an expert document analysis AI specialized in extracting specific information from invoices and financial documents.

                Your task is to analyze the provided structured text data (which includes text blocks and tables with coordinates) and extract the following information:
                1. Invoice Number
                2. Invoice Date
                3. Invoice Amount/Total

                Instructions:
                - Look through both text blocks and table data
                - Return the information in JSON format with keys: "invoice_number", "invoice_date", "invoice_amount"
                - If any field cannot be found, set its value to null
                - For dates, use YYYY-MM-DD format if possible
                - For amounts, include only the numeric value without currency symbols
                - Be precise and only extract information you are confident about

                Return only valid JSON without any additional text or explanation."""

            # Use the structured text data directly as the user message
            user_message = f"Please analyze this structured document data and extract the invoice number, date, and amount:\n\n{structured_text_data}"

            # Prepare messages for Bedrock
            messages = [
                {
                    "role": "user",
                    "content": [{"text": user_message}]
                }
            ]

            self.logger.info("Invoking Bedrock for structured data extraction")



            # Try different Claude models in order of preference
            model_ids = [
                "amazon.nova-pro-v1:0"
            ]

            for model_id in model_ids:
                try:
                    self.logger.info(f"Trying Bedrock model: {model_id}")
                    response = self.bedrock_client.converse(
                        modelId=model_id,
                        system=[{"text": system_prompt}],
                        messages=messages,
                        inferenceConfig={
                            'maxTokens': 1000,
                            'temperature': 0,
                            'topP': 1,
                        }
                    )
                    self.logger.info(f"Successfully used Bedrock model: {model_id}")
                    break
                except ClientError as model_error:
                    self.logger.warning(f"Model {model_id} failed: {model_error}")
                    if model_id == model_ids[-1]:  # Last model in list
                        # If all models fail, raise the error
                        self.logger.error("All Bedrock models failed")
                        raise
                    continue

            # Extract response text
            response_text = response['output']['message']['content'][0]['text']

            # Parse JSON response
            try:
                extracted_data = json.loads(response_text)
                self.logger.info("Successfully extracted data using Bedrock")
                return extracted_data
            except json.JSONDecodeError:
                self.logger.warning("Bedrock response was not valid JSON, attempting to clean it")
                # Try to extract JSON from response if it contains additional text
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    extracted_data = json.loads(json_match.group())
                    return extracted_data
                else:
                    raise ValueError("Could not parse JSON from Bedrock response")

        except Exception as e:
            self.logger.error(f"Error invoking Bedrock for extraction: {e}")
            raise

import json
import re
import logging
from botocore.exceptions import ClientError
import boto3

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Example structured text data input
structured_text_data = """
BLOCK: Invoice Number: Hars-1995
BLOCK: Date: 2025-08-12
BLOCK: Total: $1,450.75
Table Row: ['Description', 'Qty', 'Price']
Table Row: ['Product A', '2', '$500.00']
"""

# System prompt for Bedrock
system_prompt = """You are an expert document analysis AI specialized in extracting specific information from invoices and financial documents.

Your task is to analyze the provided structured text data (which includes text blocks and tables with coordinates) and extract the following information:
1. Invoice Number
2. Invoice Date
3. Invoice Amount/Total

Instructions:
- Look through both text blocks and table data
- Return the information in JSON format with keys: "invoice_number", "invoice_date", "invoice_amount"
- If any field cannot be found, set its value to null
- For dates, use YYYY-MM-DD format if possible
- For amounts, include only the numeric value without currency symbols
- Be precise and only extract information you are confident about

Return only valid JSON without any additional text or explanation."""

# User message to send to Bedrock
user_message = f"Please analyze this structured document data and extract the invoice number, date, and amount:\n\n{structured_text_data}"

# Prepare messages for Bedrock
messages = [
    {
        "role": "user",
        "content": [{"text": user_message}]
    }
]

# Initialize Bedrock client (update region as needed)
session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")
bedrock_client = session.client("bedrock-runtime", region_name="us-east-1")

# Model preference list
model_ids = ["amazon.nova-pro-v1:0"]

response = None

# Try models in order
for model_id in model_ids:
    try:
        logger.info(f"Trying Bedrock model: {model_id}")
        response = bedrock_client.converse(
            modelId=model_id,
            system=[{"text": system_prompt}],
            messages=messages,
            inferenceConfig={
                "maxTokens": 1000,
                "temperature": 0,
                "topP": 1,
            }
        )
        logger.info(f"Successfully used Bedrock model: {model_id}")
        break
    except ClientError as model_error:
        logger.warning(f"Model {model_id} failed: {model_error}")
        if model_id == model_ids[-1]:
            logger.error("All Bedrock models failed.")
            raise
        continue

# Extract response text
response_text = response['output']['message']['content'][0]['text']

# Parse JSON response
try:
    extracted_data = json.loads(response_text)
    logger.info("Successfully extracted data using Bedrock")
except json.JSONDecodeError:
    logger.warning("Bedrock response was not valid JSON, attempting to clean it")
    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
    if json_match:
        extracted_data = json.loads(json_match.group())
    else:
        raise ValueError("Could not parse JSON from Bedrock response")

# Final output
print(json.dumps(extracted_data, indent=2))




import pandas as pd

df = pd.read_csv(r"/home/<USER>/Documents/repositories/logistically/docs/logistically-invoice-carrier-combined-doc-paths.csv")

# filstering df by attachment_type as invoice
df = df[df['attachment_type'] == 'invoice']
df

# grounping df by carrier_name and sorting in decending order of path_to_file count
df_sorted = df.groupby('carrier_name').count().sort_values(by='path_to_file', ascending=False)
df_sorted



df_sorted.head(20)



import boto3

client = boto3.client("textract", region_name="us-east-1", aws_access_key_id="********************", aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")

with open(r"/home/<USER>/Documents/repositories/logistically/data/input_data/selected/CTECH11379561_carrier_inv-wrong_total.pdf", "rb") as f:
    image_bytes = f.read()

resp = client.analyze_document(
    Document={"Bytes": image_bytes},
    FeatureTypes=["TABLES", "FORMS"]  # FeatureTypes is valid for AnalyzeDocument
)

for block in resp.get("Blocks", []):
    if block["BlockType"] == "LINE":
        print(block["Text"])


resp

# async code to call textract


import boto3
import time
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set working directory
os.chdir("/home/<USER>/Documents/repositories/logistically")

# Initialize AWS clients
s3_client = boto3.client(
    's3',
    region_name='us-east-1',
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY")
)
textract = boto3.client(
    'textract',
    region_name='us-east-1',
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY")
)

# Local file and S3 details
local_file_path = '/home/<USER>/Documents/repositories/logistically/data/input_data/selected/1_ASITT11442689_INV-which_invoice_no_to_be_taken.pdf'  # Replace with your local file path
bucket_name = 'document-extraction-logistically'  # Replace with your S3 bucket name
document_name = os.path.basename(local_file_path)  # Use file name for S3 key

# Upload file to S3
try:
    s3_client.upload_file(local_file_path, bucket_name, document_name)
    print(f"Successfully uploaded {local_file_path} to s3://{bucket_name}/{document_name}")
except Exception as e:
    print(f"Error uploading file to S3: {str(e)}")
    exit(1)

# Start asynchronous document analysis with TABLES
try:
    response = textract.start_document_analysis(
        DocumentLocation={
            'S3Object': {
                'Bucket': bucket_name,
                'Name': document_name
            }
        },
        # FeatureTypes=['TABLES']
    )
    job_id = response['JobId']
    print(f"Started job: {job_id}")
except Exception as e:
    print(f"Error starting Textract job: {str(e)}")
    exit(1)

# Poll for job completion
while True:
    result = textract.get_document_analysis(JobId=job_id)
    status = result['JobStatus']
    if status in ['SUCCEEDED', 'FAILED']:
        break
    print("Waiting for job to complete...")
    time.sleep(5)

# Process results if succeeded
if status == 'SUCCEEDED':
    blocks = result['Blocks']
    while 'NextToken' in result:
        result = textract.get_document_analysis(JobId=job_id, NextToken=result['NextToken'])
        blocks.extend(result['Blocks'])
    
    # Extract tables
    tables = [block for block in blocks if block['BlockType'] == 'TABLE']
    for table in tables:
        print(f"Table ID: {table['Id']}")
        for relationship in table.get('Relationships', []):
            if relationship['Type'] == 'CHILD':
                cell_ids = relationship['Ids']
                cells = [block for block in blocks if block['Id'] in cell_ids]
                for cell in cells:
                    if cell['BlockType'] == 'CELL':
                        print(f"Cell [{cell['RowIndex']}][{cell['ColumnIndex']}]: {cell.get('Text', '')}")
else:
    print(f"Job failed: {status}")

import boto3
import time
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set working directory
os.chdir("/home/<USER>/Documents/repositories/logistically")

# Initialize AWS clients
s3_client = boto3.client(
    's3',
    region_name='us-east-1',
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY")
)
textract = boto3.client(
    'textract',
    region_name='us-east-1',
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY")
)

# Local file and S3 details
local_file_path = '/home/<USER>/Documents/repositories/logistically/data/input_data/selected/1_ASITT11442689_INV-which_invoice_no_to_be_taken.pdf'  # Replace with your local file path
bucket_name = 'document-extraction-logistically'  # Replace with your S3 bucket name
document_name = os.path.basename(local_file_path)  # Use file name for S3 key

# Upload file to S3
try:
    s3_client.upload_file(local_file_path, bucket_name, document_name)
    print(f"Successfully uploaded {local_file_path} to s3://{bucket_name}/{document_name}")
except Exception as e:
    print(f"Error uploading file to S3: {str(e)}")
    exit(1)

# Start asynchronous document analysis with TABLES
try:
    response = textract.start_document_analysis(
        DocumentLocation={
            'S3Object': {
                'Bucket': bucket_name,
                'Name': document_name
            }
        },
        FeatureTypes=[]
    )
    job_id = response['JobId']
    print(f"Started job: {job_id}")
except Exception as e:
    print(f"Error starting Textract job: {str(e)}")
    exit(1)

# Poll for job completion
while True:
    result = textract.get_document_analysis(JobId=job_id)
    status = result['JobStatus']
    if status in ['SUCCEEDED', 'FAILED']:
        break
    print("Waiting for job to complete...")
    time.sleep(5)

# Process results if succeeded
if status == 'SUCCEEDED':
    blocks = result['Blocks']
    while 'NextToken' in result:
        result = textract.get_document_analysis(JobId=job_id, NextToken=result['NextToken'])
        blocks.extend(result['Blocks'])
    
    # Extract tables
    tables = [block for block in blocks if block['BlockType'] == 'TABLE']
    for table in tables:
        print(f"Table ID: {table['Id']}")
        for relationship in table.get('Relationships', []):
            if relationship['Type'] == 'CHILD':
                cell_ids = relationship['Ids']
                cells = [block for block in blocks if block['Id'] in cell_ids]
                for cell in cells:
                    if cell['BlockType'] == 'CELL':
                        print(f"Cell [{cell['RowIndex']}][{cell['ColumnIndex']}]: {cell.get('Text', '')}")
else:
    print(f"Job failed: {status}")

blocks

















import boto3
import json  # Added for parsing if needed, but assuming response is already loaded

client = boto3.client("textract", region_name="us-east-1", aws_access_key_id="********************", aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")

with open(r"/home/<USER>/Documents/repositories/logistically/data/input_data/classification/1-134362.pdf", "rb") as f:
    image_bytes = f.read()

resp = client.analyze_document(
    Document={"Bytes": image_bytes},
    FeatureTypes=["TABLES", "FORMS"]  # FeatureTypes is valid for AnalyzeDocument
)

# Function to extract texts grouped by page using relationships
def extract_page_texts(response):
    page_texts = {}
    # Find all PAGE blocks
    pages = [block for block in response.get("Blocks", []) if block["BlockType"] == "PAGE"]
    
    for page_num, page in enumerate(pages, start=1):
        # Get child IDs from relationships
        child_ids = []
        for rel in page.get("Relationships", []):
            if rel["Type"] == "CHILD":
                child_ids.extend(rel["Ids"])
        
        # Collect texts from LINE blocks among children
        lines = []
        for block in response["Blocks"]:
            if block["Id"] in child_ids and block["BlockType"] == "LINE":
                lines.append(block["Text"])
        
        # Join lines for the page content
        content = "\n".join(lines)
        page_texts[page_num] = content
    
    return page_texts

# Extract page texts
page_texts = extract_page_texts(resp)

# Store all pages' information in one variable (a single string with tags)
all_pages_content = ""
for page, content in sorted(page_texts.items()):
    all_pages_content += f"<Page{page}>\n\n{content}\n\n</Page{page}>\n\n"

# Print in the desired format
print(all_pages_content)

# Example: Save to a file
with open("extracted_text.txt", "w", encoding="utf-8") as f:
    f.write(all_pages_content)






from pprint import pprint

system_prompt = """You are an expert document analysis AI specialized in extracting specific information from invoices and financial documents.

    Your task is to analyze the provided structured text data (which includes text blocks and tables with coordinates) and extract the following information:
    1. Invoice Number
    2. Invoice Date
    3. Invoice Amount/Total

    Instructions:
    - Look through both text blocks and table data
    - Return the information in JSON format with keys: "invoice_number", "invoice_date", "invoice_amount"
    - If any field cannot be found, set its value to null
    - For dates, use YYYY-MM-DD format if possible
    - For amounts, include only the numeric value without currency symbols
    - Be precise and only extract information you are confident about

    Return only valid JSON without any additional text or explanation.
"""

# Use the structured text data directly as the user message
user_message = f"Please analyze this structured document data and extract the invoice number, date, and amount:\n\n{structured_text_data}"

# Prepare messages for Bedrock
messages = [
    {
        "role": "user",
        "content": [{"text": user_message}]
    }
]

response = bedrock_client.converse(
        modelId="amazon.nova-pro-v1:0",
        system=[{"text": system_prompt}],
        messages=messages,
        inferenceConfig={
            'maxTokens': 1000,
            'temperature': 0,
            'topP': 1,
        }
        )

pprint(response)

print(response['output']['message']['content'][0]['text'])





# code to download s3 object
import boto3
from urllib.parse import urlparse
import os

def download_from_s3_uri(s3_uri, local_file_path, aws_profile=None):
    """
    Download a file from S3 using S3 URI format (s3://bucket/key)

    Args:
        s3_uri (str): S3 URI in format s3://bucket-name/path/to/file
        local_file_path (str): Local path where file should be saved
        aws_profile (str, optional): AWS profile name to use

    Returns:
        bool: True if successful, False otherwise
    """
    # Parse S3 URI
    parsed_uri = urlparse(s3_uri)
    bucket_name = parsed_uri.netloc
    s3_key = parsed_uri.path.lstrip('/')

    # Create S3 client
    if aws_profile:
        session = boto3.Session(profile_name=aws_profile)
        s3_client = session.client('s3')
    else:
        s3_client = boto3.client('s3', region_name="us-east-1",
            aws_access_key_id="********************",
            aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(local_file_path), exist_ok=True)

    # Download the file
    s3_client.download_file(bucket_name, s3_key, local_file_path)
    print(f"Successfully downloaded {s3_uri} to {local_file_path}")
    return True
# sample call
# download_from_s3_uri(
#     's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf', 
#     "/home/<USER>/Documents/repositories/logistically/T17VQU5SZ70INLN5LS8L.pdf")

download_from_s3_uri(r"s3://tms-upload-prod/account_docs/02dceb38-32ad-47e6-9497-06eea4ad1629/order_attachments/order_11630185/QURP3ZX2MQDF6YHG4HGJ.pdf",
                     r"/home/<USER>/Documents/repositories/logistically/data/input_data/classification/new/combined_doc_1.pdf")


import boto3, json
from pprint import pprint
import base64  # Only if you need to encode, but here we use bytes directly

# Initialize the Bedrock Runtime client
bedrock_runtime = boto3.client('bedrock-runtime', region_name="us-east-1",
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")

if dates are like ship date / invoice date  MM1/DD1/YYYY1 / MM2/DD2/YYYY2 then take MM2/DD2/YYYY2 as invoice date

# Specify the Amazon Nova model ID (use a multimodal-capable one like nova-lite)
model_id = 'amazon.nova-pro-v1:0'

# Path to your local PDF file (ensure it's under 25 MB for direct upload)
pdf_file_path = r'/home/<USER>/Documents/repositories/logistically/MNG2I9X2MN6QYRVFMY6V.pdf'

# Read the PDF file as bytes
with open(pdf_file_path, 'rb') as pdf_file:
    pdf_bytes = pdf_file.read()

system_prompt = '''
    ## Task Summary:
    You are an expert document analysis AI specialized in extracting specific information from invoices and financial documents. Strickly give only working json as output without explaining anything or even including 'json' word.

    ## Model Instructions:
    Your task is to analyze the provided PDF and extract the following information:
        - Vendor name - string
        - Invoice Number - string
        - Invoice Date - string in YYYY-MM-DD format (invoice may contain date in MM-DD-YYYY or MM/DD/YYYY or similar format)
        - Invoice Amount/Total - float
        - Remit to Name - string - Name to which payment must be sent to
        - Remit to address - string - Address to which payment must be sent to
    Cross check given answer again and make it absolutely correct. Check all given values and change to correct value if required
    If data is given in complex way then break the task and extract accurate value without mixing it with any other value

    ## Response Schema:
    ```json
    {
    "vendor_name": "[actual_vendor_name]",
    "invoice_number": "[actual_invoice_number]",
    "invoice_date": "[actual_date]",
    "invoice_amount": [actual_amount],
    "remit_to_name": "[actual_remit_to_name]",
    "remit_to_address": "[actual_remit_to_address]"
    }```
'''

user_message = {
    'role': 'user',
    'content': [
        {
            'document': {
                'name': 'example_pdf',
                'format': 'pdf',
                'source': {'bytes': pdf_bytes}
            }
        },
        {
            'text': '''
                    ## Context Information:
                    Context Information is given in PDF   
                    '''
        }
    ]
}

assistant_message = {
    'role': 'assistant',
    'content': [
        {
            'text': 'Here is the JSON response: ```json'
        }
    ]
}

# Call the Converse API
response = bedrock_runtime.converse(
    modelId=model_id,
    system=[{"text": system_prompt}],
    messages=[user_message, assistant_message],
    inferenceConfig={
        'temperature': 0
    }
)

# Extract and print the model's response
output = response['output']['message']['content'][0]['text']
pprint(json.loads(output.strip("```")))








{
    "dict":232
}```



# Specify the Amazon Nova model ID (use a multimodal-capable one like nova-lite)
model_id = 'amazon.nova-lite-v1:0'

# Path to your local PDF file (ensure it's under 25 MB for direct upload)
pdf_file_path = '/home/<USER>/Documents/repositories/logistically/data/input_data/extraction/processed/HENTIL11181492_INV.pdf'

# Read the PDF file as bytes
with open(pdf_file_path, 'rb') as pdf_file:
    pdf_bytes = pdf_file.read()

system_prompt = '''
    Give vendor_name, remit_to_name, bill to and bill from with explaination
'''

user_message = {
    'role': 'user',
    'content': [
        {
            'document': {
                'name': 'example_pdf',
                'format': 'pdf',
                'source': {'bytes': pdf_bytes}
            }
        },
        {
            'text': '''
                    ## Context Information:
                    Context Information is given in PDF   
                    '''
        }
    ]
}

# Call the Converse API
response = bedrock_runtime.converse(
    modelId=model_id,
    system=[{"text": system_prompt}],
    messages=[user_message],
    inferenceConfig={
        'temperature': 0
    }
)

# Extract and print the model's response
output = response['output']['message']['content'][0]['text']
pprint(json.loads(output.strip("```")))


print(output)

import boto3
import json

# Initialize Bedrock Runtime client
client = boto3.client("bedrock-runtime", region_name="us-west-2", 
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")  # Adjust region as needed


import json

# Path to your JSON file
file_path = "/home/<USER>/Documents/repositories/logistically/data/input_data/extraction/HMFL11418924_INV/HMFL11418924_INV_textract.txt"

# reading the file
with open(file_path, 'r') as file:
    user_prompt = file.read()

# Prepare the message with the document bytes
messages = [
    {
        "role": "user",
        "content": [
            {"text": user_prompt}
        ]
    }
]

# System prompt for better accuracy
system_prompt = [
    {
        "text": (
            """
                ## Task Summary:
                    You are a completely obedient accountant who is an expert at structured data extraction from invoices. Follow steps mentioned in "Model Instructions".

                ## Model Instructions:
                    Step 1: The conversion of a PDF to a text invoice is provided in UserContent in unstrucutred form. 

                    Step 2: Give output as mentioned in "Response Schema"

                    Step 3: UserContent is given in the following csv like structure 
                    === TEXT WITH COORDINATES ===
                    text, x1, y1, x2, y2
                    [actual_text], [x1], [y1], [x2], [y2]

                    Here x1, y1, x2, y2 represents standard bounding box coordinates of actual text. 

                    Step 4: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. 

                    Step 5: Find relevant information from the invoice and fill out the required output JSON file. If something is not found in the invoice then keep the respective value of the output as ''. 

                    Step 6: Check again all extraction pairs and correct it, if required, so that all information is accurately extracted from all pages in the output JSON. DO NOT ASSUME ANYTHING.

                ## Response Schema:
                    ```json
                    {
                    // The name of the vendor issuing the invoice (e.g., company or individual name).
                    "vendor_name": "[actual_vendor_name]",

                    // The unique identifier like invoice number or reference number for the invoice.
                    "invoice_number": "[actual_invoice_number]",

                    // The date when the invoice was issued, formatted as YYYY-MM-DD (e.g., "2023-01-15").
                    // Prioritize issuance date over due date or other dates unless clearly indicated.
                    "invoice_date": "[actual_date_in_YYYY-MM-DD_format]",

                    // Do not sum total at your end
                    // The total amount due on the invoice, in numeric format (e.g., 1234.56).
                    // It might be possible that total would be written without mention of "Total due" or "Total Amount" word
                    // If multiple totals exist (e.g., subtotal and tax), use the final total amount due.
                    // Invoice total must be present and you dont have to calculate it. It contains 
                    "invoice_amount": [actual_amount_in_float],

                    // The name of the entity to which payment should be sent (e.g., vendor or payment processor).
                    // Same as vendor_name without explicit remit-to indication.
                    "remit_to_name": "[actual_remit_to_name]",

                    // The full address for payment remittance (e.g., "123 Main St, Springfield, IL 62701, USA").
                    // Must be a string. Include all available address components (street, city, state, postal code, country).
                    "remit_to_address": "[actual_remit_to_address]"
                    }```
            """
        )
    }
]

modelId="amazon.nova-pro-v1:0"    #         openai.gpt-oss-20b-1:0
# Call the Converse API
response = client.converse(
    modelId=modelId,
    messages=messages,
    system=system_prompt,
    inferenceConfig={"temperature": 0, "maxTokens": 10240}  # Low temperature for consistency; adjust maxTokens for large docs
)
if modelId=="openai.gpt-oss-20b-1:0":
    print(response['output']['message']['content'][1]['text'])
else:
    print(response['output']['message']['content'])



# printing reasoningText
if modelId=="openai.gpt-oss-20b-1:0":
    print(response['output']['message']['content'][0]['reasoningContent']['reasoningText']['text'])
else:
    print(response['output']['message']['content'][0]['reasoningContent']['reasoningText']['text'])

response



import boto3
import json
import os
from dotenv import load_dotenv

load_dotenv()

os.chdir("/home/<USER>/Documents/repositories/logistically")

# Initialize Bedrock Runtime client
client_east_1 = boto3.client("bedrock-runtime", region_name="us-east-1", 
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"))  # Adjust region as needed


import json

# Adding parent folder as sys path
import sys
sys.path.append("/home/<USER>/Documents/repositories/logistically")
from textract_processor import process_file_with_textract

# Path to your JSON file 
file_path = "/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/failed_in_shortlisted_v2/********.pdf"

textract_response, formatted_text, _ = process_file_with_textract(
    file_path=file_path,
    bucket_name="document-extraction-logistically"
)

# Prepare the message with the document bytes
messages = [
    {
        "role": "user",
        "content": [
            {"text": formatted_text}
        ]
    }
]

# System prompt for better accuracy
system_prompt = [
    {
        "text": (
            """
            ## Task Summary:
                You are a completely obedient accountant who is an expert at structured data extraction from US based invoices. Follow steps mentioned in "Model Instructions".

            ## Model Instructions:
                Step 1: The conversion of a PDF to a text invoice is provided in UserContent in the following csv like structure 
                === TEXT WITH COORDINATES ===
                text, x1, y1, x2, y2
                [actual_text], [x1], [y1], [x2], [y2]

                Here x1, y1, x2, y2 represents standard bounding box coordinates of actual text.  

                Step 2: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. Strickly look at x1 co-ordinate for horizontal location of text

                Step 3: Find relevant information from the invoice and fill out the required output JSON file. If something is not found in the invoice then keep the respective value of the output as ''. 

                Step 4: Check again all extraction pairs and correct it, if required, so that all information is accurately extracted from all pages in the output JSON. 
                
                Step 5: Strickly refer and follow comments mentioned in "Response Schema" json structure and make sure that all information is extracted correctly. DO NOT ASSUME ANYTHING.

            ## Response Schema:
                ```json
                {
                    // The name of the vendor issuing the invoice (e.g., company or individual name).
                    "vendor_name": "[actual_vendor_name]",

                    // The unique identifier like invoice number or reference number for the invoice.
                    "invoice_number": "[actual_invoice_number]",

                    // The date when the invoice was issued, strickly formatted as "YYYY-MM-DD" (e.g., "2023-12-31").
                    // Give only if date is explicitly mentioned as invoice date or Bill date or Invoiced.
                    // In invoice date might be in MM/DD/YYYY or MM-DD-YYYY or similar format. Convert it to YYYY-MM-DD format.
                    "invoice_date": "[actual_date_in_YYYY-MM-DD_format]",

                    // The total amount due on the invoice in numeric format (e.g., 1234.56).
                    // Invoice total must be present and you dont have to calculate it.
                    // If multiple totals exist (e.g., subtotal and tax), use the final total amount due.
                    // Must find invoice total even if it is not explicitly mentioned as "Total" or "Invoice Total" or "Amount Due" etc. It might be present as float value somewhere in lower poertion of invoice as generally mentioned. Do not calculate or sum it.
                    "invoice_amount": [actual_amount_in_float_required],

                    // .
                    "remit_to": {

                        // The name of the entity to which payment should be sent (Mentioned as remit to in invoice).
                        "remit_to_name": "[actual_remit_to_name]",

                        // The primary street address line for payment remittance (e.g., "123 Main St").
                        "remit_to_address1": "[actual_remit_to_address1]",

                        // The secondary street address line for payment remittance (e.g., "Suite 100"). Optional, use null if not applicable.
                        "remit_to_address_2": "[actual_remit_to_address_2]",

                        // The city for payment remittance (e.g., "Springfield").
                        "remit_to_city": "[actual_remit_to_city]",

                        // The state or province for payment remittance (e.g., "IL" or "Ontario").
                        "remit_to_state_province": "[actual_remit_to_state_province]",

                        // The postal or ZIP code for payment remittance (e.g., "62701").
                        "remit_to_postal_code": "[actual_remit_to_postal_code]",

                        // The country for payment remittance (e.g., "USA" or "Canada").
                        "remit_to_country": "[actual_remit_to_country]"
                    } or 
                    // If remit to details are not explicitly mentioned as "Remit to" in the document then keep it empty.
                    "null"
                }```
            """
        )
    }
]

modelId="amazon.nova-pro-v1:0" #  "amazon.nova-pro-v1:0"    #         openai.gpt-oss-20b-1:0          nova-premier-v1:0
# Call the Converse API
response = client_east_1.converse(
    modelId=modelId,
    messages=messages,
    system=system_prompt,
    inferenceConfig={"temperature": 0, "maxTokens": 10000, 'topP': 1}  # Low temperature for consistency; adjust maxTokens for large docs
)
if modelId=="openai.gpt-oss-20b-1:0":
    print(response['output']['message']['content'][1]['text'])
else:

    print(response['output']['message']['content'][0]['text'])

print(formatted_text)










modelId="amazon.nova-pro-v1:0" #  "amazon.nova-pro-v1:0"    #         openai.gpt-oss-20b-1:0          nova-premier-v1:0
# Call the Converse API
response = client_east_1.converse(
    modelId=modelId,
    messages=messages,
    system=system_prompt,
    inferenceConfig={"temperature": 0, "maxTokens": 10000, 'topP': 1}  # Low temperature for consistency; adjust maxTokens for large docs
)
if modelId=="openai.gpt-oss-20b-1:0":
    print(response['output']['message']['content'][1]['text'])
else:

    print(response['output']['message']['content'][0]['text'])





response['output']['message']['content'][0]['text']

import json
import logging
import re
from typing import Any, Dict

# Configure logging once in your app
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_json_from_string(raw: str) -> Dict[str, Any]:
    """
    Safely load a JSON object from a raw string that may contain
    code fences, language hints (```json), and escaped newlines.

    Args:
        raw (str): Input string containing JSON (possibly wrapped in markdown fences).

    Returns:
        dict: Parsed JSON object.

    Raises:
        ValueError: If parsing fails.
    """
    try:
        # Remove surrounding ```json ... ``` fences if present
        cleaned = re.sub(r"^```[a-zA-Z]*\n|\n```$", "", raw.strip())

        # Now load JSON
        return json.loads(cleaned)

    except json.JSONDecodeError as e:
        logger.error("Invalid JSON string: %s", e)
        raise ValueError(f"Failed to parse JSON: {e}") from e


variable = load_json_from_string(response['output']['message']['content'][0]['text'])
variable

type(variable)

import re
import json
import ast
from typing import Any, Dict

def extract_json_from_backticks(text: str) -> Dict[str, Any]:
    """
    Extract JSON enclosed in triple backticks (``` ... ```) and parse it.
    - Looks for the first opening ``` and the next closing ```.
    - Accepts optional fence label (e.g. ```json or ```json\n).
    - Tries json.loads first, then tries extracting {...} or [...] slices,
      then falls back to a best-effort ast.literal_eval after simple literal replacements.
    - Never raises: returns a dict with 'ok' True and 'data' on success,
      or 'ok' False with 'error' and the extracted 'content' on failure.

    Returns:
      {'working_json': True, 'data': <parsed object>}
      OR
      {'working_json': False, 'error': 'message', 'content': '<extracted_text (trimmed)>'}
    """
    # Find first fenced block fenced by triple backticks
    fence_pat = re.compile(r'```(?:[^\n]*)\n?(.*?)```', re.DOTALL)
    m = fence_pat.search(text)
    if not m:
        return {'working_json': False, 'error': 'No triple-backtick fenced block found', 'content': ''}

    content = m.group(1)
    stripped = content.strip()

    # 1) Try strict JSON
    try:
        parsed = json.loads(stripped)
        return {'working_json': True, 'data': parsed}
    except json.JSONDecodeError as first_err:
        last_err = str(first_err)

    # 2) Try to locate a JSON object/array substring within the fence (first {...} or [...])
    for start_ch, end_ch in (('{', '}'), ('[', ']')):
        s_idx = content.find(start_ch)
        e_idx = content.rfind(end_ch)
        if s_idx != -1 and e_idx != -1 and e_idx > s_idx:
            candidate = content[s_idx:e_idx+1].strip()
            try:
                parsed = json.loads(candidate)
                return {'working_json': True, 'data': parsed}
            except json.JSONDecodeError as e:
                last_err = f"{last_err}; fallback slice JSON error: {e}"

    # 3) Best-effort Python-literal parse (handles single quotes, trailing commas sometimes)
    #    Convert JS literals to Python equivalents (simple replacement).
    #    This may modify inside strings in extreme cases, but it's a pragmatic fallback.
    try:
        alt = re.sub(r'\bnull\b', 'None', content, flags=re.IGNORECASE)
        alt = re.sub(r'\btrue\b', 'True', alt, flags=re.IGNORECASE)
        alt = re.sub(r'\bfalse\b', 'False', alt, flags=re.IGNORECASE)

        # Attempt to isolate braces/brackets first (if present)
        s_idx = alt.find('{')
        e_idx = alt.rfind('}')
        if s_idx != -1 and e_idx != -1 and e_idx > s_idx:
            alt_candidate = alt[s_idx:e_idx+1]
        else:
            s_idx = alt.find('[')
            e_idx = alt.rfind(']')
            alt_candidate = alt[s_idx:e_idx+1] if (s_idx != -1 and e_idx != -1 and e_idx > s_idx) else alt

        parsed = ast.literal_eval(alt_candidate)
        return {'working_json': True, 'data': parsed}
    except Exception as e:
        last_err = f"{last_err}; ast fallback error: {e}"

    # If everything fails, return failure with debugging info (trim content for safety)
    debug_content = (stripped[:1000] + '...') if len(stripped) > 1000 else stripped
    return {
        'working_json': False,
        'error': 'Failed to parse JSON from fenced block; last error: ' + last_err,
        'content': debug_content
    }


string_1 = '''An
fghfg

'gh
''"
```json\n{\n    "vendor_name": "HAP TRUCKING LTD.",\n    "invoice_number": "U5940",\n    "invoice_date": "2025-07-31",\n    "invoice_amount": 3000.00,\n    "remit_to": {\n        "remit_to_name": "HAP TRUCKING LTD.",\n        "remit_to_address1": "7954 WEBSTER ROAD",\n        "remit_to_address_2": "DELTA, BC V4G 1G6",\n        "remit_to_city": "DELTA",\n        "remit_to_state_province": "BC",\n        "remit_to_postal_code": "V4G 1G6",\n        "remit_to_country": "USA"\n    }\n}\n```
sdfgd
ffgf '

'''
variable = extract_json_from_backticks(string_1)
variable

string_1















import boto3

client = boto3.client("bedrock", region_name="us-east-1")
response = client.list_foundation_models()

for model in response["modelSummaries"]:
    print("Model Name:", model.get("modelName"))
    print(" → modelId:", model.get("modelId"))
    print(" → modelArn:", model.get("modelArn"))
    print("Supported Input:", model.get("inputModalities"))
    print("Supported Output:", model.get("outputModalities"))
    print("-" * 40)


import boto3

def simple_converse():
    client = boto3.client("bedrock-runtime", region_name="us-east-1")

    response = client.converse(
        modelId="arn:aws:bedrock:us-east-1::foundation-model/amazon.nova-premier-v1:0:8k",  # pick any available model
        messages=[
            {"role": "user", "content": [{"text": "Write a haiku about AI"}]}
        ],
    )

    # Print the text output
    print(response["output"]["message"]["content"][0]["text"])


if __name__ == "__main__":
    simple_converse()



import boto3

def simple_converse():
    client = boto3.client("bedrock-runtime", region_name="us-east-1")

    response = client.converse(
        modelId="amazon.nova-premier-v1:0",   # ✅ Correct model ID
        messages=[
            {"role": "user", "content": [{"text": "Write a haiku about AI"}]}
        ],
    )

    print(response["output"]["message"]["content"][0]["text"])

if __name__ == "__main__":
    simple_converse()


import boto3

def simple_converse():
    client = boto3.client("bedrock-runtime", region_name="us-east-1")

    response = client.converse(
        modelId="arn:aws:bedrock:us-east-1:123456789012:inference-profile/amazon.nova-premier-v1:0",
        messages=[
            {"role": "user", "content": [{"text": "Write a haiku about AI"}]}
        ],
    )

    print(response["output"]["message"]["content"][0]["text"])

if __name__ == "__main__":
    simple_converse()


import boto3

client = boto3.client("bedrock", region_name="us-east-1")

print("Provisioned models:")
resp = client.list_provisioned_model_throughputs()
for item in resp["provisionedModelSummaries"]:
    print("  Name:", item.get("modelArn"))
    print("  Model Id:", item.get("modelId"))
    print("  Status:", item.get("status"))
    print("-" * 40)

print("Inference profiles:")
resp = client.list_inference_profiles()
for item in resp["inferenceProfileSummaries"]:
    print("  Name:", item.get("name"))
    print("  ARN :", item.get("inferenceProfileArn"))
    print("-" * 40)


import boto3

def simple_converse():
    client = boto3.client("bedrock-runtime", region_name="us-east-1")

    response = client.converse(
        modelId="arn:aws:bedrock:us-east-1:270373480468:inference-profile/us.amazon.nova-premier-v1:0",
        messages=[
            {"role": "user", "content": [{"text": "Write a haiku about AI"}]}
        ],
    )

    print(response["output"]["message"]["content"][0]["text"])

if __name__ == "__main__":
    simple_converse()
















import json

# Adding parent folder as sys path
import sys
sys.path.append("/home/<USER>/Documents/repositories/logistically")
from textract_processor import process_file_with_textract

# Path to your JSON file 
file_path = "/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/invoice_shortlisted_v2/Unishippers _ Worldwide Express/processed/250802W002752.pdf"

textract_response, formatted_text, reconstructed_layout = process_file_with_textract(
    file_path=file_path,
    bucket_name="document-extraction-logistically"
)



print(reconstructed_layout)

























print(formatted_text)

print("=== TEXT WITH COORDINATES ===\ntext, x1, y1, x2, y2\nPLAN6003 PAGE 3, 0.7718, 0.0141, 0.9673, 0.0230\n\"REMIT TO: Performance Freight Systems, Inc\", 0.0419, 0.0252, 0.2721, 0.0341\nPO Box 210947, 0.0421, 0.0372, 0.1203, 0.0442\n\"Performance Freight Systems, Inc.\", 0.3654, 0.0281, 0.6598, 0.0424\nMilwaukee WI 53221-8016, 0.0422, 0.0477, 0.1839, 0.0549\nORIGINAL INVOICE, 0.4182, 0.0447, 0.6131, 0.0561\nSHIP DATE, 0.0417, 0.0613, 0.0985, 0.0680\nREFERENCE NUMBER, 0.1968, 0.0613, 0.3125, 0.0679\nP.O. NUMBER, 0.3333, 0.0613, 0.4043, 0.0681\nFREIGHT TERMS, 0.5133, 0.0613, 0.6000, 0.0680\nPAYMENT DUE DATE, 0.6603, 0.0647, 0.7702, 0.0715\n07/29/25, 0.0438, 0.0775, 0.1330, 0.0872\nMPE11450414, 0.1937, 0.0780, 0.3197, 0.0868\nSEE BELOW, 0.3352, 0.0786, 0.4372, 0.0866\nPPD, 0.5567, 0.0785, 0.5883, 0.0865\n08/15/25, 0.6621, 0.0776, 0.7513, 0.0873\nCONSIGNEE, 0.0416, 0.0909, 0.1060, 0.0976\nMODE3125 (773)254-3929, 0.1240, 0.0929, 0.3663, 0.1032\nORIGIN TERM, 0.3796, 0.0910, 0.4504, 0.0975\nORIGIN REF #, 0.5127, 0.0908, 0.5840, 0.0976\nMODERN PROCESS EQUIPMENT, 0.0426, 0.1085, 0.3207, 0.1182\nPERFORMANCE SHIPMENT NUMBER, 0.6603, 0.1146, 0.8553, 0.1213\n3125 S KOLIN AVENUE, 0.0440, 0.1228, 0.2623, 0.1317\nDEST TERM, 0.3797, 0.1290, 0.4409, 0.1355\nDEST REF #, 0.5133, 0.1289, 0.5747, 0.1357\nCHICAGO IL 60623, 0.0431, 0.1378, 0.2264, 0.1467\n60039969-00, 0.6855, 0.1376, 0.8096, 0.1467\n30, 0.4175, 0.1527, 0.4360, 0.1614\n60039969, 0.5114, 0.1526, 0.6000, 0.1616\nSHIPPER, 0.0416, 0.1655, 0.0885, 0.1724\nNORD800, 0.1359, 0.1679, 0.2147, 0.1764\nADVANCE, 0.3807, 0.1677, 0.4333, 0.1748\nBEYOND, 0.5161, 0.1679, 0.5613, 0.1749\nBILL TO:, 0.6576, 0.1657, 0.7015, 0.1725\nPLAN6003, 0.7199, 0.1677, 0.8090, 0.1764\nNORD GEAR CORP, 0.0429, 0.1832, 0.2026, 0.1914\n\"PLANET FREIGHT, INC\", 0.6611, 0.1832, 0.8797, 0.1929\n800 NORD DR, 0.0442, 0.1975, 0.1695, 0.2064\nPO BOX 472, 0.6612, 0.1975, 0.7744, 0.2064\nPO BOX 367, 0.0433, 0.2125, 0.1559, 0.2213\n\"NEENAH, WI 54957\", 0.6603, 0.2129, 0.8442, 0.2225\nWAUNAKEE WI 53597, 0.0428, 0.2272, 0.2381, 0.2364\nPIECES, 0.0572, 0.2426, 0.0959, 0.2492\nPLTS, 0.1217, 0.2427, 0.1486, 0.2493\nDESCRIPTION OF ARTICLES AND SPECIAL MARKINGS, 0.2199, 0.2425, 0.5044, 0.2494\nCLASS, 0.5941, 0.2427, 0.6302, 0.2493\nWEIGHT, 0.6676, 0.2426, 0.7106, 0.2491\nRATE, 0.7636, 0.2427, 0.7918, 0.2491\nCHARGES, 0.8705, 0.2427, 0.9237, 0.2493\n3, 0.0909, 0.2574, 0.0981, 0.2661\n3, 0.1375, 0.2575, 0.1447, 0.2661\nSPEED REDUCERS, 0.1605, 0.2579, 0.3196, 0.2662\n070, 0.6040, 0.2573, 0.6346, 0.2662\n277, 0.6966, 0.2573, 0.7275, 0.2661\n126.31, 0.7553, 0.2574, 0.8210, 0.2661\n2 Pcs @ 31 X 47 X 30 Inches, 0.1603, 0.2724, 0.4713, 0.2817\nMINIMUM CHRG. FOR LANE, 0.1593, 0.2876, 0.4136, 0.2961\n60.00, 0.8848, 0.2871, 0.9381, 0.2961\nILLINOIS TOLL, 0.1612, 0.3027, 0.3090, 0.3111\n7.00, 0.8954, 0.3020, 0.9380, 0.3112\nFUEL SURCHARGE: 32.70%, 0.1598, 0.3170, 0.4131, 0.3261\n19.62, 0.8845, 0.3172, 0.9376, 0.3259\n1 31\"X47\"X30\"X2, 0.1609, 0.3319, 0.3314, 0.3409\nQUOTE# 609139. PO#: 1029271., 0.1598, 0.3469, 0.4797, 0.3570\nPICKUP# 204454286, 0.1596, 0.3619, 0.3549, 0.3709\nBOL: MPE11450414, 0.1598, 0.3771, 0.3433, 0.3857\nPO: 1029271, 0.1599, 0.3919, 0.2844, 0.4007\nPU: 204454286, 0.1599, 0.4069, 0.3085, 0.4158\nCONTINUED ON PAGE:, 0.1713, 0.4371, 0.3756, 0.4457\n2, 0.4283, 0.4369, 0.4360, 0.4453\nFOR PROPER CREDIT WE MUST HAVE A COPY OF THE INVOICE SUMMARY (REMITTANCE COPY) OR THE CORRECT FREIGHT BILL NO. AND THE AMOUNT OF EACH BILL RETURNED WITH PAYMENT, 0.0541, 0.4487, 0.9324, 0.4560\nFOR CUSTOMER SERVICE PLEASE CALL 414-385-5440, 0.2664, 0.4584, 0.6724, 0.4678\n\"REMIT TO: Performance Freight Systems, Inc\", 0.0420, 0.5025, 0.2727, 0.5114\nPO Box 210947, 0.0422, 0.5144, 0.1203, 0.5216\n\"Performance Freight Systems, Inc.\", 0.3656, 0.5054, 0.6598, 0.5199\nMilwaukee WI 53221-8016, 0.0419, 0.5251, 0.1838, 0.5324\nORIGINAL INVOICE, 0.4180, 0.5219, 0.6130, 0.5334\nSHIP DATE, 0.0417, 0.5385, 0.0984, 0.5450\nREFERENCE NUMBER, 0.1969, 0.5385, 0.3124, 0.5455\nP.O. NUMBER, 0.3334, 0.5386, 0.4045, 0.5454\nFREIGHT TERMS, 0.5132, 0.5386, 0.6001, 0.5455\nPAYMENT DUE DATE, 0.6606, 0.5419, 0.7702, 0.5486\n07/29/25, 0.0439, 0.5547, 0.1330, 0.5646\nMPE11450414, 0.1940, 0.5554, 0.3194, 0.5640\nSEE BELOW, 0.3353, 0.5558, 0.4373, 0.5639\nPPD, 0.5566, 0.5557, 0.5882, 0.5638\n08/15/25, 0.6618, 0.5547, 0.7512, 0.5644\nCONSIGNEE, 0.0417, 0.5682, 0.1060, 0.5747\nMODE3125 (773) 254-3929, 0.1241, 0.5701, 0.3667, 0.5804\nORIGIN TERM, 0.3795, 0.5682, 0.4503, 0.5747\nORIGIN REF #, 0.5131, 0.5681, 0.5841, 0.5750\nMODERN PROCESS EQUIPMENT, 0.0426, 0.5857, 0.3205, 0.5952\nPERFORMANCE SHIPMENT NUMBER, 0.6601, 0.5918, 0.8554, 0.5987\n3125 S KOLIN AVENUE, 0.0441, 0.6001, 0.2624, 0.6088\nDEST TERM, 0.3797, 0.6063, 0.4408, 0.6128\nDEST REF #, 0.5132, 0.6063, 0.5747, 0.6129\nCHICAGO IL 60623, 0.0431, 0.6152, 0.2263, 0.6238\n60039969-00, 0.6855, 0.6150, 0.8092, 0.6238\n30, 0.4175, 0.6299, 0.4360, 0.6385\n60039969, 0.5117, 0.6300, 0.6002, 0.6388\nSHIPPER, 0.0417, 0.6429, 0.0883, 0.6499\nNORD800, 0.1359, 0.6454, 0.2146, 0.6535\nADVANCE, 0.3808, 0.6453, 0.4338, 0.6519\nBEYOND, 0.5165, 0.6455, 0.5615, 0.6516\nBILL TO:, 0.6575, 0.6430, 0.7019, 0.6497\nPLAN6003, 0.7196, 0.6449, 0.8091, 0.6536\nNORD GEAR CORP, 0.0430, 0.6606, 0.2026, 0.6690\n\"PLANET FREIGHT, INC\", 0.6610, 0.6608, 0.8799, 0.6702\n800 NORD DR, 0.0443, 0.6751, 0.1696, 0.6838\nPO BOX 472, 0.6610, 0.6748, 0.7744, 0.6839\nPO BOX 367, 0.0433, 0.6898, 0.1560, 0.6989\n\"NEENAH, WI 54957\", 0.6605, 0.6897, 0.8446, 0.6996\nWAUNAKEE WI 53597, 0.0429, 0.7047, 0.2380, 0.7136\nPIECES, 0.0572, 0.7199, 0.0959, 0.7268\nPLTS, 0.1218, 0.7202, 0.1486, 0.7267\nDESCRIPTION OF ARTICLES AND SPECIAL MARKINGS, 0.2200, 0.7200, 0.5044, 0.7269\nCLASS, 0.5943, 0.7198, 0.6304, 0.7270\nWEIGHT, 0.6675, 0.7202, 0.7104, 0.7264\nRATE, 0.7634, 0.7201, 0.7920, 0.7268\nCHARGES, 0.8706, 0.7198, 0.9240, 0.7269\nPUNUM: 40984, 0.1597, 0.7346, 0.2959, 0.7434\nQ1: 609141, 0.1598, 0.7496, 0.2726, 0.7593\nQN: 609139, 0.1597, 0.7645, 0.2730, 0.7741\n3, 0.0910, 0.9142, 0.0982, 0.9228\n3, 0.1375, 0.9142, 0.1448, 0.9229\n277, 0.6968, 0.9136, 0.7276, 0.9224\n86.62, 0.8839, 0.9141, 0.9380, 0.9229\nFOR PROPER CREDIT WE MUST HAVE A COPY OF THE INVOICE SUMMARY (REMITTANCE COPY) OR THE CORRECT FREIGHT BILL NO. AND THE AMOUNT OF EACH BILL RETURNED WITH PAYMENT, 0.0542, 0.9259, 0.9321, 0.9336\nFOR CUSTOMER SERVICE PLEASE CALL 414-385-5440, 0.2664, 0.9358, 0.6722, 0.9452\n")

textract_response









# reading /home/<USER>/Documents/repositories/logistically/docs/logistically-invoice-carrier-combined-doc-paths.csv file to pandas
import pandas as pd
df = pd.read_csv(r"/home/<USER>/Documents/repositories/logistically/docs/logistically-invoice-carrier-combined-doc-paths.csv")
df.head()

# arranging in decending order of total count by carrier_name and attachment_type=invoice

df_invoice = df[df['attachment_type'] == 'invoice']
df_invoice_sorted = df_invoice.groupby('carrier_name').count().sort_values(by='path_to_file', ascending=False)
df_invoice_sorted.head(20)

import pandas as pd
df = pd.read_csv(r"/home/<USER>/Documents/repositories/logistically/docs/logistically_attachment_data_with_invoice_info.csv")
df.head()

df.columns

['original_file_name', 'invoice_number', 'invoice_date', 'invoice_amount', 'vendor_name', 'remit_to_name', 'remit_to_address1', 'remit_to_address_2', 'remit_to_city', 'remit_to_state_province', 'remit_to_postal_code', 'remit_to_country']

# arranging in decending order of total count by carrier_name and attachment_type=invoice

df_invoice = df[df['attachment_type'] == 'invoice']
df_invoice_sorted = df_invoice.groupby('carrier_name').count().sort_values(by='path_to_file', ascending=False)
df_invoice_sorted.head(10)

df_invoice

# printing total number of duplicate original_file_name bifurgataed by original_file_name & VENDOR NAME
df_invoice.groupby(['original_file_name', 'vendor_name']).count().sort_values(by='path_to_file', ascending=False).head(30)

df_invoice.groupby('original_file_name').count().sort_values(by='path_to_file', ascending=False).head(10)



import pandas as pd
df = pd.read_excel(r"data/evaluation/csv_evaluation_20250822_220310 - Full file_v2_v2_complete_20250825_205904_v3.xlsx")
df.head()

# printing total number of files processed
df['Moved PDF Path'].value_counts()

df['Moved PDF Path'].nunique()








# reading /home/<USER>/Documents/repositories/gemini-cli-demo/csv_evaluation_20250822_220310 - Full file_v2_v2_complete_20250825_205904_v3.xlsx
import pandas as pd

df = pd.read_excel(r"/home/<USER>/Documents/repositories/logistically/data/evaluation/csv_evaluation_20250822_220310 - Full file_v2_v2_complete_20250825_205904_v3.xlsx")
df.head(5)

# filtering where AI cross check value is Seems FALSE
df_false = df[df['AI Cross check'] == 'Seems FALSE']
df_false.head(5)


# unique values in "Moved PDF Path" in df_false
unique_paths = df_false['Moved PDF Path'].unique()
unique_paths

len(unique_paths)

# copying pdfs in unique_paths to /home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/failed_in_shortlisted_v2
import shutil
import os
import pathlib

for path in unique_paths:
    try:
        source_path = pathlib.Path(path)
        destination_path = pathlib.Path(r"/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/failed_in_shortlisted_v2") / source_path.name
        if not destination_path.exists():
            shutil.copy2(source_path, destination_path)
        print(f"Copied {source_path} to {destination_path}")
    except Exception as e:
        print(f"Error moving {source_path}: {e}")















import pandas as pd

# reading /home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv
df_cd = pd.read_csv(r"/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv")
df_cd.head()

# grouping by vendor_name and counting number of files
df_cd.groupby('vendor_name').count().sort_values(by='path_to_file', ascending=False).head(25)

# grouping by vendor_name and counting no of un





















import boto3
import base64  # Only if you need to encode, but here we use bytes directly

# Initialize the Bedrock Runtime client
bedrock_runtime = boto3.client('bedrock-runtime', region_name="us-east-1",
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")

# Specify the Amazon Nova model ID (use a multimodal-capable one like nova-lite)
model_id = 'amazon.nova-lite-v1:0'

# Path to your local PDF file (ensure it's under 25 MB for direct upload)
pdf_file_path = '/home/<USER>/Documents/repositories/logistically/data/input_data/extraction/processed/HENTIL11181492_INV.pdf'

# Read the PDF file as bytes
with open(pdf_file_path, 'rb') as pdf_file:
    pdf_bytes = pdf_file.read()

system_prompt = '''
    ## Task Summary:
    You are an expert document classification AI specialized in classifying logistics documents. Strickly give only working json as output without explaining anything or even including 'json' word.

    ## Model Instructions:
    Your task is to analyze the provided pdf data and infer the following information:
        - Vendor name - string
        - Invoice Number - string
        - Invoice Date - string in YYYY-MM-DD format (invoice may contain date in MM-DD-YYYY or MM/DD/YYYY or similar format)
        - Invoice Amount/Total - float
        - Remit to Name - string - Name to which payment must be sent to
        - Remit to address - string - Address to which payment must be sent to

    ## Response Schema:
    ```json
    {
    "vendor_name": "[actual_vendor_name]",
    "invoice_number": "[actual_invoice_number]",
    "invoice_date": "[actual_date]",
    "invoice_amount": [actual_amount],
    "remit_to_name": "[actual_remit_to_name]",
    "remit_to_address": "[actual_remit_to_address]"
    }```
'''

user_message = {
    'role': 'user',
    'content': [
        {
            'document': {
                'name': 'example_pdf',
                'format': 'pdf',
                'source': {'bytes': pdf_bytes}
            }
        },
        {
            'text': '''
                    ## Context Information:
                    Context Information is given in PDF   
                    '''
        }
    ]
}

assistant_message = {
    'role': 'assistant',
    'content': [
        {
            'text': 'Here is the JSON response: ```json'
        }
    ]
}

# Call the Converse API
response = bedrock_runtime.converse(
    modelId=model_id,
    system=[{"text": system_prompt}],
    messages=[user_message, assistant_message],
    inferenceConfig={
        'temperature': 0
    }
)

# Extract and print the model's response
output = response['output']['message']['content'][0]['text']
pprint(json.loads(output.strip("```")))




import boto3
import json

# Initialize Bedrock Runtime client
client = boto3.client("bedrock-runtime", region_name="us-east-1", 
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")  # Adjust region as needed

# Define the tool configuration with enum and schema for page details
tool_config = {
    "tools": [
        {
            "toolSpec": {
                "name": "classify_logistics_doc_type",
                "description": "Extract logistics document type, description, and page details from multi-page documents",
                "inputSchema": {
                    "json": {
                        "type": "object",
                        "properties": {
                            "documents": {
                                "type": "array",
                                "description": "Array of extracted document type summaries from the multi-page document",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "doc_type": {
                                            "type": "string",
                                            "enum": [
                                                "bol", "pod", "rate_confirmation", "cust_rate_confirmation",
                                                "clear_to_pay", "scale_ticket", "freight_bill", "log",
                                                "fuel_receipt", "invoice", "cust_invoice",
                                                "combined_carrier_documents", "pack_list", "po",
                                                "comm_invoice", "customs_doc", "nmfc_cert", "other",
                                                "coa", "tender_from_cust", "lumper_receipt",
                                                "so_confirmatin", "po_confirmatin", "pre_pull_receipt",
                                                "ingate", "outgate"
                                            ],
                                            "description": "Short code for the logistics document type (must match enum)"
                                        },
                                        "doc_type_description": {
                                            "type": "string",
                                            "description": "Human-readable full description of the document type (e.g., 'Bill of Lading' for 'bol')"
                                        },
                                        "pages": {
                                            "type": "array",
                                            "description": "Array of pages associated with this document type",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "page_number": {
                                                        "type": "integer",
                                                        "description": "Page number (starting from 1)"
                                                    }
                                                },
                                                "required": ["page_number"]
                                            }
                                        },
                                        "total_pages": {
                                            "type": "integer",
                                            "description": "Total number of pages for this document type"
                                        }
                                    },
                                    "required": ["doc_type", "doc_type_description", "pages", "total_pages"]
                                }
                            }
                        },
                        "required": ["documents"]
                    }
                }
            }
        }
    ]
}

pdf_path = "/home/<USER>/Documents/repositories/logistically/data/input_data/classification/1-134362.pdf"  # Replace with your PDF file path

# Function to extract document types from a multi-page PDF
# Load the PDF as bytes
with open(pdf_path, "rb") as file:
    document_bytes = file.read()

# User prompt instructing the model to classify each page
user_prompt = (
    "Analyze this multi-page logistics document. For each distinct document type found"
)

# Prepare the message with the document bytes
messages = [
    {
        "role": "user",
        "content": [
            {"text": user_prompt},
            {
                "document": {
                    "format": "pdf",  # Specify format (Nova supports pdf, docx, etc.)
                    "name": "logistics_document",
                    "source": {"bytes": document_bytes}
                }
            }
        ]
    }
]

# System prompt for better accuracy
system_prompt = [
    {
        "text": (
            '''
                ## Task Summary:
                    You are an expert document classification AI specialized in classifying logistics documents. Strickly use the extract_logistics_doc_type tool to output structured results.

                ## Model Instructions:
                    Your task is to analyze the provided pdf data and infer the following information:
                    - doc_type - type of document from given enum
                    - doc_type_description - description of doc_type
                    - page_number - particular page number for which the document type is found
                    - total_pages - total number of pages for the document type

                ## Enum details for doc_type:
                    bol = Bill of Lading - A legal document issued by a carrier to a shipper, detailing the type, quantity, and destination of goods being shipped, serving as a receipt and contract.

                    pod = Proof of Delivery - A document confirming that goods have been delivered to the recipient, often including signatures, dates, and condition notes.

                    rate_confirmation = Carrier Rate Confirmation - An agreement between a shipper and carrier outlining the rates, terms, and conditions for transporting a specific load.

                    cust_rate_confirmation = Customer Rate Confirmation - A document confirming rates and terms for a customer, similar to carrier rate confirmation but focused on customer agreements.

                    clear_to_pay = Clear to Pay - Authorization indicating that an invoice or bill is approved for payment after verification of services rendered.

                    scale_ticket = Scale Ticket - A record from a weighing scale documenting the weight of a vehicle or load, used for billing and compliance in freight.

                    freight_bill = Freight Bill - An invoice from the carrier to the shipper detailing charges for transportation services, including fees and surcharges.

                    log = Log - A record of activities, such as driver logs or shipment tracking logs, often required for regulatory compliance like hours of service.

                    fuel_receipt = Fuel Receipt - Documentation of fuel purchases, used for expense tracking, reimbursements, and tax purposes in transportation.

                    invoice = Carrier Invoice - A bill issued by the carrier for services provided, including details of the shipment and associated costs.

                    cust_invoice = Customer Invoice - A bill sent to the customer for logistics services, outlining charges for shipping, handling, or related fees.

                    combined_carrier_documents = Combined Carrier Documents - A bundled set of multiple carrier-related documents, such as BOLs and invoices, compiled for a single shipment or audit.

                    pack_list = Packing List - An itemized list of contents in a shipment, used for inventory verification during packing, shipping, and receiving.

                    po = Purchase Order - A document issued by a buyer to a seller, specifying the types, quantities, and agreed prices for products or services.

                    comm_invoice = Commercial Invoice - A customs document detailing the value, description, and origin of goods for international shipments, used for duties and taxes.

                    customs_doc = Customs Document - General paperwork required for international trade, including declarations, certificates, or forms for customs clearance.

                    nmfc_cert = NMFC Certificate - Certification related to the National Motor Freight Classification, used to standardize freight descriptions and rates.

                    other = Other - A catch-all category for any logistics document that doesn't fit into the predefined types.

                    coa = Certificate of Analysis - A document verifying the quality and composition of goods (e.g., chemicals or materials), often required for regulatory compliance.

                    tender_from_cust = Load Tender from Customer - An offer or request from a customer to a carrier to transport a load, including details like pickup, delivery, and requirements.

                    lumper_receipt = Lumper Receipt - Documentation of payment to lumpers (laborers who load/unload trucks), used for reimbursement and expense tracking.

                    so_confirmation = Sales Order Confirmation - Acknowledgment of a sales order, confirming details like items, quantities, prices, and delivery terms.

                    po_confirmation = Purchase Order Confirmation - Acknowledgment from a seller confirming receipt and acceptance of a purchase order.

                    pre_pull_receipt = Pre-Pull Receipt - Documentation for pre-pulling a container or load from storage before the scheduled pickup, often for efficiency in ports or warehouses.

                    ingate = Ingate Document - Record of a container or vehicle entering a terminal or facility, including inspections and timestamps.

                    outgate = Outgate Document - Record of a container or vehicle exiting a terminal or facility, confirming release and any associated fees

                    Strickly include details of each page in the extract_logistics_doc_type tool call and do not skip any page.
            '''
        )
    }
]

# Call the Converse API
response = client.converse(
    modelId="us.amazon.nova-pro-v1:0",  # Use Nova Pro for multi-page complexity; alternatives: nova-lite-v1:0 or nova-premier-v1:0
    messages=messages,
    system=system_prompt,
    toolConfig=tool_config,
    inferenceConfig={"temperature": 0}  # Low temperature for consistency; adjust maxTokens for large docs
)

# Parse the structured output from toolUse
tool_use_content = next((item for item in response["output"]["message"]["content"] if "toolUse" in item), None)
if tool_use_content:
    extracted_data = tool_use_content["toolUse"]["input"]
else:
    raise ValueError("No toolUse found in response")

print(extracted_data)

pprint(extracted_data)

user_prompt














                ## Response Schema:
                    ```json
                    {
                    "documents": [
                        {
                        "doc_type": "[actual_doc_type]",
                        "doc_type_description": "[actual_doc_type_description]",
                        "pages": [
                            {
                            "page_number": [actual_page_number]
                            }
                        ],
                        "total_pages": [total_pages]
                        },
                    ]```

local_pdf_path = '/home/<USER>/Documents/repositories/logistically/data/input_data/classification/001A.pdf'

import boto3
import time
from botocore.exceptions import ClientError
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict

# Configuration
s3_bucket = 'document-extraction-logistically'
s3_key = 'input/1-134362.pdf'

# Initialize clients
s3 = boto3.client('s3', region_name='us-east-1',
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")
textract = boto3.client('textract', region_name='us-east-1',
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")

# Upload PDF to S3
try:
    s3.upload_file(local_pdf_path, s3_bucket, s3_key)
except ClientError as e:
    raise Exception(f'Error uploading file: {e}')

# Start Textract text detection job
response = textract.start_document_text_detection(
    DocumentLocation={'S3Object': {'Bucket': s3_bucket, 'Name': s3_key}}
)
job_id = response['JobId']

# Poll for completion with adaptive delay
delay = 2  # Start with short delay
max_delay = 10  # Max delay for longer jobs
while True:
    result = textract.get_document_text_detection(JobId=job_id, MaxResults=1)
    status = result['JobStatus']
    if status in ['SUCCEEDED', 'FAILED']:
        break
    time.sleep(delay)
    delay = min(delay * 1.5, max_delay)  # Exponential backoff

if status == 'FAILED':
    raise Exception('Textract job failed.')

# Fetch all blocks with pagination
all_blocks = []
next_token = None
while True:
    kwargs = {'JobId': job_id, 'MaxResults': 1000}
    if next_token:
        kwargs['NextToken'] = next_token
    result = textract.get_document_text_detection(**kwargs)
    all_blocks.extend(result['Blocks'])
    next_token = result.get('NextToken')
    if not next_token:
        break

# Group blocks by page and filter lines in one pass
pages = defaultdict(list)
for block in all_blocks:
    if 'Page' in block and block['BlockType'] == 'LINE' and 'Text' in block:
        pages[block['Page']].append(block['Text'])

# Extract text for a single page
def extract_page_text(page_num, texts):
    if texts:
        joined_text = '\n'.join(texts)
        return f"<page{page_num}>\n\n{joined_text}\n\n</page{page_num}>\n\n"
    return ""

# Process pages concurrently
output = []
with ThreadPoolExecutor() as executor:
    futures = [executor.submit(extract_page_text, page_num, texts) 
               for page_num, texts in sorted(pages.items())]
    output = [f.result() for f in futures if f.result()]

# Combine all page outputs into a single string
output = '\n'.join(output)
output

print(output)

import boto3
import json

# Initialize Bedrock Runtime client
client = boto3.client("bedrock-runtime", region_name="us-west-2", 
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")  # Adjust region as needed

# Define the tool configuration with enum and schema for page details
tool_config = {
    "tools": [
        {
            "toolSpec": {
                "name": "classify_logistics_doc_type",
                "description": "Extract logistics document type, description, and page details from multi-page documents",
                "inputSchema": {
                    "json": {
                        "type": "object",
                        "properties": {
                            "documents": {
                                "type": "array",
                                "description": "Array of extracted document type summaries from the multi-page document",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "doc_type": {
                                            "type": "string",
                                            "enum": [
                                                "bol", "pod", "rate_confirmation", "cust_rate_confirmation",
                                                "clear_to_pay", "scale_ticket", "freight_bill", "log",
                                                "fuel_receipt", "invoice", "cust_invoice",
                                                "combined_carrier_documents", "pack_list", "po",
                                                "comm_invoice", "customs_doc", "nmfc_cert", "other",
                                                "coa", "tender_from_cust", "lumper_receipt",
                                                "so_confirmatin", "po_confirmatin", "pre_pull_receipt",
                                                "ingate", "outgate"
                                            ],
                                            "description": "Short code for the logistics document type (must match enum)"
                                        },
                                        "doc_type_description": {
                                            "type": "string",
                                            "description": "Human-readable full description of the document type (e.g., 'Bill of Lading' for 'bol')"
                                        },
                                        "pages": {
                                            "type": "array",
                                            "description": "Array of pages associated with this document type",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "page_number": {
                                                        "type": "integer",
                                                        "description": "Page number (starting from 1)"
                                                    }
                                                },
                                                "required": ["page_number"]
                                            }
                                        },
                                        "total_pages": {
                                            "type": "integer",
                                            "description": "Total number of pages for this document type"
                                        }
                                    },
                                    "required": ["doc_type", "doc_type_description", "pages", "total_pages"]
                                }
                            }
                        },
                        "required": ["documents"]
                    }
                }
            }
        }
    ]
}



# User prompt instructing the model to classify each page
user_prompt = (
    "Analyze this multi-page logistics document. For each distinct document type found"
)

# Prepare the message with the document bytes
messages = [
    {
        "role": "user",
        "content": [
            {"text": output}
        ]
    }
]

# System prompt for better accuracy
system_prompt = [
    {
        "text": (
            '''
                ## Task Summary:
                    You are an expert document classification AI specialized in classifying logistics documents. Strickly use the extract_logistics_doc_type tool to output structured results.

                ## Model Instructions:
                    Your task is to analyze the provided pdf data and infer the following information:
                    - doc_type - type of document from given enum
                    - doc_type_description - description of doc_type
                    - page_number - particular page number for which the document type is found
                    - total_pages - total number of pages for the document type

                ## Enum details for doc_type:
                    bol = Bill of Lading - A legal document issued by a carrier to a shipper, detailing the type, quantity, and destination of goods being shipped, serving as a receipt and contract.

                    pod = Proof of Delivery - A document confirming that goods have been delivered to the recipient, often including signatures, dates, and condition notes.

                    rate_confirmation = Carrier Rate Confirmation - An agreement between a shipper and carrier outlining the rates, terms, and conditions for transporting a specific load.

                    cust_rate_confirmation = Customer Rate Confirmation - A document confirming rates and terms for a customer, similar to carrier rate confirmation but focused on customer agreements.

                    clear_to_pay = Clear to Pay - Authorization indicating that an invoice or bill is approved for payment after verification of services rendered.

                    scale_ticket = Scale Ticket - A record from a weighing scale documenting the weight of a vehicle or load, used for billing and compliance in freight.

                    freight_bill = Freight Bill - An invoice from the carrier to the shipper detailing charges for transportation services, including fees and surcharges.

                    log = Log - A record of activities, such as driver logs or shipment tracking logs, often required for regulatory compliance like hours of service.

                    fuel_receipt = Fuel Receipt - Documentation of fuel purchases, used for expense tracking, reimbursements, and tax purposes in transportation.

                    invoice = Carrier Invoice - A bill issued by the carrier for services provided, including details of the shipment and associated costs.

                    cust_invoice = Customer Invoice - A bill sent to the customer for logistics services, outlining charges for shipping, handling, or related fees.

                    combined_carrier_documents = Combined Carrier Documents - A bundled set of multiple carrier-related documents, such as BOLs and invoices, compiled for a single shipment or audit.

                    pack_list = Packing List - An itemized list of contents in a shipment, used for inventory verification during packing, shipping, and receiving.

                    po = Purchase Order - A document issued by a buyer to a seller, specifying the types, quantities, and agreed prices for products or services.

                    comm_invoice = Commercial Invoice - A customs document detailing the value, description, and origin of goods for international shipments, used for duties and taxes.

                    customs_doc = Customs Document - General paperwork required for international trade, including declarations, certificates, or forms for customs clearance.

                    nmfc_cert = NMFC Certificate - Certification related to the National Motor Freight Classification, used to standardize freight descriptions and rates.

                    other = Other - A catch-all category for any logistics document that doesn't fit into the predefined types.

                    coa = Certificate of Analysis - A document verifying the quality and composition of goods (e.g., chemicals or materials), often required for regulatory compliance.

                    tender_from_cust = Load Tender from Customer - An offer or request from a customer to a carrier to transport a load, including details like pickup, delivery, and requirements.

                    lumper_receipt = Lumper Receipt - Documentation of payment to lumpers (laborers who load/unload trucks), used for reimbursement and expense tracking.

                    so_confirmation = Sales Order Confirmation - Acknowledgment of a sales order, confirming details like items, quantities, prices, and delivery terms.

                    po_confirmation = Purchase Order Confirmation - Acknowledgment from a seller confirming receipt and acceptance of a purchase order.

                    pre_pull_receipt = Pre-Pull Receipt - Documentation for pre-pulling a container or load from storage before the scheduled pickup, often for efficiency in ports or warehouses.

                    ingate = Ingate Document - Record of a container or vehicle entering a terminal or facility, including inspections and timestamps.

                    outgate = Outgate Document - Record of a container or vehicle exiting a terminal or facility, confirming release and any associated fees

                    Strickly include details of each page in the extract_logistics_doc_type tool call and do not skip any page.

                ## Response Schema:
                    ```json
                    {
                    "documents": [
                        {
                        "doc_type": "[actual_doc_type]",
                        "doc_type_description": "[actual_doc_type_description]",
                        "pages": [
                            {
                            "page_number": [actual_page_number]
                            }
                        ],
                        "total_pages": [total_pages]
                        },
                    ]```
            '''
        )
    }
]

# Call the Converse API
response = client.converse(
    modelId="openai.gpt-oss-20b-1:0",  # Use Nova Pro for multi-page complexity; alternatives: nova-lite-v1:0 or nova-premier-v1:0
    messages=messages,
    system=system_prompt,
    toolConfig=tool_config,
    inferenceConfig={"temperature": 0}  # Low temperature for consistency; adjust maxTokens for large docs
)

response

# printing reasoningText
print(response['output']['message']['content'][0]['reasoningContent']['reasoningText']['text'])

response['output']['message']['content'][1]

response['output']['message']['content'][1]['toolUse']['input']









import os
import pandas as pd

def get_folder_structure(root_dir):
    """
    Collect folder structure data and return it as a pandas DataFrame.
    Columns: Main Folder, Subfolder, Total Files
    """
    try:
        # Initialize lists to store data
        main_folders = []
        subfolders = []
        file_counts = []
        
        # Get the main folder name
        main_folder = os.path.basename(os.path.abspath(root_dir))
        
        # Walk through directory
        for root, dirs, files in os.walk(root_dir):
            # Get the relative path from the root directory
            rel_path = os.path.relpath(root, root_dir)
            
            # If we're in the root directory, skip to process subfolders
            if rel_path == '.':
                continue
                
            # Count total files in the current subfolder
            file_count = len(files)
            
            # Append data to lists
            main_folders.append(main_folder)
            subfolders.append(rel_path)
            file_counts.append(file_count)
        
        # Create DataFrame
        df = pd.DataFrame({
            'Main Folder': main_folders,
            'Subfolder': subfolders,
            'Total Files': file_counts
        })
        
        return df
    
    except (NotADirectoryError, FileNotFoundError) as e:
        print(f"Error: Invalid directory path - {str(e)}")
        return pd.DataFrame()  # Return empty DataFrame on error
    except PermissionError as e:
        print(f"Error: Permission denied - {str(e)}")
        return pd.DataFrame()  # Return empty DataFrame on error

if __name__ == "__main__":
    # Specify the directory path here
    directory = r"/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/invoice_shortlisted"
    df = get_folder_structure(directory)
    
    # Display the DataFrame
    if not df.empty:
        print("\nFolder Structure DataFrame:")
        print(df)
    else:
        print("No data to display.")

# shorting df by total files
df = df.sort_values(by='Total Files', ascending=False)
df.head(20)

import os
import pandas as pd

def get_files_in_directory(directory):
    # List to store file paths and names
    file_data = []
    
    # Walk through the directory and its subfolders
    for root, _, files in os.walk(directory):
        for file in files:
            # Get the full file path
            file_path = os.path.join(root, file)
            # Append the path and file name to the list
            file_data.append([file_path, file])
    
    # Create a DataFrame with two columns: Path and File Name
    df = pd.DataFrame(file_data, columns=['Path', 'File Name'])
    return df

# Example usage
directory_path = r"/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/invoice_shortlisted"
try:
    # Get the file data
    file_df = get_files_in_directory(directory_path)
    
    # Display the results
    print("\nFiles found in the directory and its subfolders:")
    print(file_df)
    
    # Optionally, save to a CSV file
    file_df.to_csv('file_list.csv', index=False)
    print("\nData saved to 'file_list.csv'")
except Exception as e:
    print(f"An error occurred: {e}")

file_df

# printing total files with same name group by
file_df.groupby('File Name').count().sort_values(by='Path', ascending=False).head(20)



import pathlib

def count_files_by_status(folder_path):
    """
    Count files in a folder and its subfolders, categorizing them as 'processed' or 'not processed'
    based on whether their parent folder is named 'processed'.
    
    Args:
        folder_path (str): Path to the root folder to start the search.
    
    Returns:
        tuple: (processed_count, not_processed_count)
    """
    processed_count = 0
    not_processed_count = 0
    
    # Convert string path to Path object
    root_folder = pathlib.Path(folder_path)
    
    # Walk through all files recursively
    for file_path in root_folder.rglob("*"):
        if file_path.is_file():  # Check if it's a file
            # Check if any parent folder is named 'processed'
            if any(part == "processed" for part in file_path.parent.parts):
                processed_count += 1
            else:
                not_processed_count += 1
    
    return processed_count, not_processed_count

# Example usage
if __name__ == "__main__":
    folder_path = input("Enter the folder path to scan: ")
    try:
        processed, not_processed = count_files_by_status(folder_path)
        print(f"Processed files: {processed}")
        print(f"Not processed files: {not_processed}")
    except FileNotFoundError:
        print(f"Error: The folder '{folder_path}' does not exist.")
    except PermissionError:
        print(f"Error: Permission denied accessing '{folder_path}'.")
    except Exception as e:
        print(f"An error occurred: {str(e)}")

