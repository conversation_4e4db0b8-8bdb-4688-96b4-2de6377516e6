import json
import time
import boto3
import os
import argparse
import logging
import glob
import shutil
import re
import csv
from datetime import datetime
from pathlib import Path
from typing import Optional
from botocore.exceptions import ClientError
import concurrent.futures
from threading import Lock

os.chdir(r"/home/<USER>/Documents/repositories/logistically")

def setup_logging():
    """Setup logging configuration."""
    # Create logs directory if it doesn't exist
    logs_dir = Path("data/logs")
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"textract_bedrock_automation_{timestamp}.log"

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Also log to console
        ]
    )

    return logging.getLogger(__name__)

class TextractBedrockAutomation:
    def __init__(self, region: str, bucket_name: str, temp_input_prefix: str, temp_output_prefix: str, logger=None, debug_mode=False):
        self.logger = logger or logging.getLogger(__name__)
        self.debug_mode = debug_mode  # Set to True to enable debugger stops on exceptions

        self.textract_client = boto3.client('textract', region_name=region)
        self.bedrock_client = boto3.client('bedrock-runtime', region_name=region)
        self.s3_client = boto3.client('s3', region_name=region)
        self.sts_client = boto3.client('sts')

        self.bucket_name = bucket_name
        self.temp_input_prefix = temp_input_prefix
        self.temp_output_prefix = temp_output_prefix
        self.region = region
        self.aws_account_id = ************

        # Initialize tracking CSV file path
        self.tracking_csv_path = "data/output_data/extraction/file_tracking.csv"
        self.tracking_lock = Lock()

        self.logger.info(f"Initialized TextractBedrockAutomation in region {region}")

    def _handle_exception(self, e: Exception, context: str, reraise: bool = True):
        """Handle exceptions with optional debug mode support."""
        self.logger.error(f"Error in {context}: {e}")
        if reraise:
            raise

    def update_tracking_csv(self, original_pdf_path: str, moved_pdf_path: str, extracted_json_path: str,
                           invoice_number: str, vendor_name: str, processing_status: str):
        """Update the tracking CSV file with file processing information."""
        try:
            with self.tracking_lock:
                # Ensure the directory exists
                os.makedirs(os.path.dirname(self.tracking_csv_path), exist_ok=True)

                # Check if file exists to determine if we need headers
                file_exists = os.path.exists(self.tracking_csv_path)

                # Prepare the row data
                row_data = {
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'original_pdf_path': original_pdf_path,
                    'moved_pdf_path': moved_pdf_path,
                    'extracted_json_path': extracted_json_path,
                    'extracted_json_filename': os.path.basename(extracted_json_path),
                    'invoice_number': invoice_number or '',
                    'vendor_name': vendor_name or '',
                    'processing_status': processing_status,
                    'pdf_filename': os.path.basename(original_pdf_path)
                }

                # Write to CSV
                with open(self.tracking_csv_path, 'a', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['timestamp', 'original_pdf_path', 'moved_pdf_path', 'extracted_json_path',
                                'extracted_json_filename', 'invoice_number', 'vendor_name', 'processing_status', 'pdf_filename']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    # Write header if file is new
                    if not file_exists:
                        writer.writeheader()
                        self.logger.info(f"Created new tracking CSV file: {self.tracking_csv_path}")

                    # Write the data row
                    writer.writerow(row_data)

                self.logger.info(f"Updated tracking CSV with: {os.path.basename(original_pdf_path)} -> {os.path.basename(extracted_json_path)}")

        except Exception as e:
            self.logger.error(f"Error updating tracking CSV: {e}")
            # Don't raise the exception as this shouldn't stop the main processing

    def get_aws_account_id(self) -> str:
        """Retrieve AWS account ID."""
        return self.sts_client.get_caller_identity().get('Account')

    def upload_file_to_s3(self, local_file_path: str) -> str:
        """Upload local file to S3 and return S3 URI."""
        try:
            file_name = os.path.basename(local_file_path)
            s3_key = f"{self.temp_input_prefix}/{file_name}"
            self.logger.info(f"Uploading {local_file_path} to s3://{self.bucket_name}/{s3_key}")
            self.s3_client.upload_file(local_file_path, self.bucket_name, s3_key)
            s3_uri = f"s3://{self.bucket_name}/{s3_key}"
            self.logger.info(f"Successfully uploaded to {s3_uri}")
            return s3_uri
        except ClientError as e:
            self.logger.error(f"Error uploading file to S3: {e}")
            raise

    def download_file_from_s3(self, s3_uri: str, local_output_path: str):
        """Download file from S3 to local path."""
        try:
            s3_key = '/'.join(s3_uri.split('/')[3:])
            self.logger.info(f"Downloading {s3_uri} to {local_output_path}")

            # Ensure output directory exists
            os.makedirs(os.path.dirname(local_output_path), exist_ok=True)

            self.s3_client.download_file(self.bucket_name, s3_key, local_output_path)
            self.logger.info(f"Successfully downloaded to {local_output_path}")

            with open(local_output_path, 'r') as f:
                return json.load(f)
        except ClientError as e:
            self.logger.error(f"Error downloading file from S3: {e}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing JSON from downloaded file: {e}")
            raise



    def invoke_textract(self, s3_uri: str, use_sync: bool = True) -> dict:
        """Invoke Textract to extract text from document. Prioritizes sync for faster processing."""
        try:
            # Parse S3 URI to get bucket and key
            s3_parts = s3_uri.replace('s3://', '').split('/', 1)
            bucket = s3_parts[0]
            key = s3_parts[1]

            self.logger.info(f"Starting Textract text extraction for {s3_uri}")

            # Always try synchronous detection first for faster processing
            try:
                self.logger.info("Attempting synchronous Textract text detection")
                response = self.textract_client.detect_document_text(
                    Document={
                        'S3Object': {
                            'Bucket': bucket,
                            'Name': key
                        }
                    }
                )
                self.logger.info("Synchronous Textract text detection successful")
                return {'sync_response': response, 'is_sync': True}
            except ClientError as sync_error:
                # Check if it's a document size error (common reason for sync failure)
                error_code = sync_error.response.get('Error', {}).get('Code', '')
                if error_code in ['InvalidParameterException', 'UnsupportedDocumentException']:
                    self.logger.info(f"Document too large for sync processing ({error_code}), using async")
                else:
                    self.logger.warning(f"Synchronous Textract failed: {sync_error}")
                self.logger.info("Falling back to asynchronous Textract")

            # Asynchronous text detection - only text, no tables or forms for speed
            response = self.textract_client.start_document_text_detection(
                DocumentLocation={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
                # No FeatureTypes specified = text only (fastest and cheapest)
            )

            job_id = response['JobId']
            self.logger.info(f"Textract async text detection job started with ID: {job_id}")

            return {'JobId': job_id, 'is_sync': False}
        except ClientError as e:
            self.logger.error(f"Error starting Textract job: {e}")
            raise

    def check_textract_job_status(self, textract_response: dict) -> dict:
        """Check the status of the Textract job or return sync response."""
        try:
            # Handle synchronous response
            if textract_response.get('is_sync'):
                self.logger.info("Using synchronous Textract response")
                return textract_response['sync_response']

            # Handle asynchronous response
            job_id = textract_response.get('JobId')
            if not job_id:
                raise ValueError("No JobId found in Textract response")

            self.logger.info(f"Monitoring Textract job status for ID: {job_id}")
            while True:
                response = self.textract_client.get_document_text_detection(JobId=job_id)
                status = response.get('JobStatus')
                self.logger.info(f"Textract job status: {status}")

                if status in ['SUCCEEDED', 'FAILED']:
                    self.logger.info(f"Textract job completed with status: {status}")
                    return response
                elif status == 'IN_PROGRESS':
                    # Reduced sleep time for faster polling
                    time.sleep(5)
                else:
                    raise ValueError(f"Unexpected job status: {status}")
        except ClientError as e:
            self.logger.error(f"Error checking Textract job status: {e}")
            raise

    def convert_textract_to_structured_format(self, textract_response: dict) -> str:
        """Convert Textract response to structured text format for LLM processing. Text only for speed."""
        try:
            blocks = textract_response.get('Blocks', [])

            # Extract text blocks with coordinates in the required format
            text_lines = []

            # Process LINE blocks for text with coordinates (most efficient)
            for block in blocks:
                if block['BlockType'] == 'LINE':
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues

                    # Convert to x1, y1, x2, y2 format
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)

                    text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

            # If no LINE blocks found (sync response), use WORD blocks
            if not text_lines:
                self.logger.info("No LINE blocks found, using WORD blocks for text extraction")
                for block in blocks:
                    if block['BlockType'] == 'WORD':
                        bbox = block.get('Geometry', {}).get('BoundingBox', {})
                        text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues

                        # Convert to x1, y1, x2, y2 format
                        x1 = bbox.get('Left', 0)
                        y1 = bbox.get('Top', 0)
                        x2 = x1 + bbox.get('Width', 0)
                        y2 = y1 + bbox.get('Height', 0)

                        text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

            # Build the structured text format (text only, no tables for speed)
            structured_text_parts = []

            # Add header
            structured_text_parts.append("=== TEXT WITH COORDINATES ===")
            structured_text_parts.append("text, x1, y1, x2, y2")

            # Add text lines
            for line in text_lines:
                structured_text_parts.append(line)

            structured_text = "\n".join(structured_text_parts)

            self.logger.info(f"Extracted {len(text_lines)} text lines from document")
            return structured_text

        except Exception as e:
            self.logger.error(f"Error converting Textract response to structured format: {e}")
            raise

    def _extract_table_for_text_format(self, table_block: dict, block_map: dict) -> list:
        """Extract table data in text format for LLM processing."""
        try:
            # Get relationships to find cells
            relationships = table_block.get('Relationships', [])
            cell_ids = []

            for relationship in relationships:
                if relationship['Type'] == 'CHILD':
                    cell_ids.extend(relationship['Ids'])

            # Group cells by row and column
            cells_by_position = {}
            for cell_id in cell_ids:
                cell_block = block_map.get(cell_id)
                if cell_block and cell_block['BlockType'] == 'CELL':
                    row_index = cell_block.get('RowIndex', 0)
                    col_index = cell_block.get('ColumnIndex', 0)

                    if row_index not in cells_by_position:
                        cells_by_position[row_index] = {}

                    # Get cell text
                    cell_text = self._get_cell_text(cell_block, block_map).replace(',', ';')  # Replace commas
                    cells_by_position[row_index][col_index] = cell_text

            # Convert to text format
            table_lines = []
            for row_index in sorted(cells_by_position.keys()):
                row_cells = cells_by_position[row_index]
                row_data = []
                for col_index in sorted(row_cells.keys()):
                    row_data.append(row_cells[col_index])
                table_lines.append(", ".join(row_data))

            return table_lines

        except Exception as e:
            self._handle_exception(e, "extracting table data for text format", reraise=False)
            return []

    def _extract_table_data(self, table_block: dict, all_blocks: dict) -> Optional[dict]:
        """Extract table data from Textract blocks."""
        try:
            # Create a mapping of block IDs to blocks for quick lookup
            block_map = {block['Id']: block for block in all_blocks}

            table_data = {
                'table_id': table_block['Id'],
                'confidence': table_block.get('Confidence', 0),
                'coordinates': table_block.get('Geometry', {}).get('BoundingBox', {}),
                'rows': []
            }

            # Get relationships to find cells
            relationships = table_block.get('Relationships', [])
            cell_ids = []

            for relationship in relationships:
                if relationship['Type'] == 'CHILD':
                    cell_ids.extend(relationship['Ids'])

            # Group cells by row and column
            cells_by_position = {}
            for cell_id in cell_ids:
                cell_block = block_map.get(cell_id)
                if cell_block and cell_block['BlockType'] == 'CELL':
                    row_index = cell_block.get('RowIndex', 0)
                    col_index = cell_block.get('ColumnIndex', 0)

                    if row_index not in cells_by_position:
                        cells_by_position[row_index] = {}

                    # Get cell text
                    cell_text = self._get_cell_text(cell_block, block_map)

                    cells_by_position[row_index][col_index] = {
                        'text': cell_text,
                        'confidence': cell_block.get('Confidence', 0),
                        'coordinates': cell_block.get('Geometry', {}).get('BoundingBox', {})
                    }

            # Convert to ordered rows
            for row_index in sorted(cells_by_position.keys()):
                row_data = []
                row_cells = cells_by_position[row_index]
                for col_index in sorted(row_cells.keys()):
                    row_data.append(row_cells[col_index])
                table_data['rows'].append(row_data)

            return table_data

        except Exception as e:
            self.logger.error(f"Error extracting table data: {e}")
            return None

    def _get_cell_text(self, cell_block: dict, block_map: dict) -> str:
        """Extract text from a table cell."""
        try:
            cell_text = ""
            relationships = cell_block.get('Relationships', [])

            for relationship in relationships:
                if relationship['Type'] == 'CHILD':
                    for word_id in relationship['Ids']:
                        word_block = block_map.get(word_id)
                        if word_block and word_block['BlockType'] == 'WORD':
                            cell_text += word_block.get('Text', '') + " "

            return cell_text.strip()
        except Exception:
            return ""

    def save_textract_response(self, textract_response: dict, file_name: str) -> str:
        """Save raw AWS Textract response to local folder."""
        try:
            # Create folder with same name as file (without extension)
            name_without_ext = os.path.splitext(file_name)[0]
            textract_folder = os.path.join("data/input_data/extraction", name_without_ext)
            os.makedirs(textract_folder, exist_ok=True)

            # Save raw Textract response
            response_file_path = os.path.join(textract_folder, f"{name_without_ext}_textract_response.json")
            with open(response_file_path, 'w') as f:
                json.dump(textract_response, f, indent=2)

            self.logger.info(f"Saved Textract response to: {response_file_path}")
            return response_file_path

        except Exception as e:
            self.logger.error(f"Error saving Textract response: {e}")
            raise

    def load_textract_response(self, file_name: str) -> Optional[dict]:
        """Load existing AWS Textract response from local folder."""
        try:
            name_without_ext = os.path.splitext(file_name)[0]
            textract_folder = os.path.join("data/input_data/extraction", name_without_ext)
            response_file_path = os.path.join(textract_folder, f"{name_without_ext}_textract_response.json")

            if os.path.exists(response_file_path):
                with open(response_file_path, 'r') as f:
                    textract_response = json.load(f)
                self.logger.info(f"Loaded existing Textract response from: {response_file_path}")
                return textract_response
            else:
                return None

        except Exception as e:
            self.logger.error(f"Error loading Textract response: {e}")
            return None

    def save_textract_data(self, structured_text: str, file_name: str) -> str:
        """Save Textract structured text data to local folder with same name as input file."""
        try:
            # Create folder with same name as file (without extension)
            name_without_ext = os.path.splitext(file_name)[0]
            textract_folder = os.path.join("data/input_data/extraction", name_without_ext)
            os.makedirs(textract_folder, exist_ok=True)

            # Save structured text data
            textract_file_path = os.path.join(textract_folder, f"{name_without_ext}_textract.txt")
            with open(textract_file_path, 'w') as f:
                f.write(structured_text)

            self.logger.info(f"Saved Textract data to: {textract_file_path}")
            return textract_file_path

        except Exception as e:
            self.logger.error(f"Error saving Textract data: {e}")
            raise

    def invoke_bedrock_extraction(self, structured_text_data: str) -> dict:
        """Use Bedrock to extract invoice number, date, and amount from structured text."""
        try:
            # Create system prompt for invoice data extraction
            system_prompt = """You are an expert document analysis AI specialized in extracting specific information from invoices and financial documents.

                Your task is to analyze the provided structured text data (which includes text blocks and tables with coordinates) and extract the following information:
                1. Invoice Number
                2. Invoice Date
                3. Invoice Amount/Total

                Instructions:
                - Look through both text blocks and table data
                - Return the information in JSON format with keys: "invoice_number", "invoice_date", "invoice_amount"
                - If any field cannot be found, set its value to null
                - For dates, use YYYY-MM-DD format if possible
                - For amounts, include only the numeric value without currency symbols
                - Be precise and only extract information you are confident about

                Return only valid JSON without any additional text or explanation."""
            
            system_prompt_nova = """
                ## Task Summary:
                    You are a completely obedient accountant who is an expert at structured data extraction from invoices. Follow these steps to perform the complete task: 

                ## Model Instructions:
                    Step 1: The conversion of a PDF to a text invoice is provided in UserContent in unstrucutred form. 

                    Step 2: Give output in the given structure:

                    Step 3: Analyze UserContent completely that is given in the following JSON structure 
                    === TEXT WITH COORDINATES ===
                    text, x1, y1, x2, y2
                    [actual_text], [x1], [y1], [x2], [y2]

                    Here x1, y1, x2, y2 represents standard bounding box coordinates of actual text. 

                    Step 4: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. 

                    Step 5: Find relevant information from the invoice and fill out the required output JSON file. If something is not found in the invoice then keep the respective value of the output as ''. 

                    Step 6: Check again for missing or wrong extraction and strictly include items accurately from all pages in the output JSON. DO NOT ASSUME ANYTHING.

                ## Response Schema:
                    ```json
                    {
                    "vendor_name": "[actual_vendor_name]",
                    "invoice_number": "[actual_invoice_number]",
                    "invoice_date": "[actual_date_in_YYYY-MM-DD_format]",
                    "invoice_amount": [actual_amount_in_float],
                    "remit_to_name": "[actual_remit_to_name]",
                    "remit_to_address": "[actual_remit_to_address]"
                    }```
            """

            system_prompt_openai = """
            ## Task Summary:
                You are a completely obedient accountant who is an expert at structured data extraction from US based invoices. Follow steps mentioned in "Model Instructions".

            ## Model Instructions:
                Step 1: The conversion of a PDF to a text invoice is provided in UserContent in the following csv like structure 
                === TEXT WITH COORDINATES ===
                text, x1, y1, x2, y2
                [actual_text], [x1], [y1], [x2], [y2]

                Here x1, y1, x2, y2 represents standard bounding box coordinates of actual text.  

                Step 2: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. Strickly look at x1 co-ordinate for horizontal location of text

                Step 3: Find relevant information from the invoice and fill out the required output JSON file. If something is not found in the invoice then keep the respective value of the output as ''. 

                Step 4: Check again all extraction pairs and correct it, if required, so that all information is accurately extracted from all pages in the output JSON. 
                
                Step 5: Strickly refer and follow comments mentioned in "Response Schema" json structure and make sure that all information is extracted correctly. DO NOT ASSUME ANYTHING.

            ## Response Schema:
                ```json
                {
                    // The name of the vendor issuing the invoice (e.g., company or individual name).
                    "vendor_name": "[actual_vendor_name]",

                    // The unique identifier like invoice number or reference number for the invoice.
                    "invoice_number": "[actual_invoice_number]",

                    // The date when the invoice was issued, strickly formatted as "YYYY-MM-DD" (e.g., "2023-12-31").
                    // Give only if date is explicitly mentioned as invoice date or Bill date or Invoiced.
                    // In invoice date might be in MM/DD/YYYY or MM-DD-YYYY or similar format. Convert it to YYYY-MM-DD format.
                    "invoice_date": "[actual_date_in_YYYY-MM-DD_format]",

                    // The total amount due on the invoice in numeric format (e.g., 1234.56).
                    // Invoice total must be present and you dont have to calculate it.
                    // If multiple totals exist (e.g., subtotal and tax), use the final total amount due.
                    // Must find invoice total even if it is not explicitly mentioned as "Total" or "Invoice Total" or "Amount Due" etc. It might be present as float value somewhere in lower poertion of invoice as generally mentioned. Do not calculate or sum it.
                    "invoice_amount": [actual_amount_in_float_required],

                    // Extremely important to include remit to details only and only if it is explicitly mentioned as "Remit to" or "Send Payment To" or "Make Checks Payable To" or similar text in the document otherwise keep it empty.
                    "remit_to": {

                        // The name of the entity to which payment should be sent (Mentioned as remit to in invoice).
                        "remit_to_name": "[actual_remit_to_name]",

                        // The primary street address line for payment remittance (e.g., "123 Main St").
                        "remit_to_address1": "[actual_remit_to_address1]",

                        // The secondary street address line for payment remittance (e.g., "Suite 100"). Optional, use null if not applicable.
                        "remit_to_address_2": "[actual_remit_to_address_2]",

                        // The city for payment remittance (e.g., "Springfield").
                        "remit_to_city": "[actual_remit_to_city]",

                        // The state or province for payment remittance (e.g., "IL" or "Ontario").
                        "remit_to_state_province": "[actual_remit_to_state_province]",

                        // The postal or ZIP code for payment remittance (e.g., "62701").
                        "remit_to_postal_code": "[actual_remit_to_postal_code]",

                        // The country for payment remittance (e.g., "USA" or "Canada").
                        "remit_to_country": "[actual_remit_to_country]"
                    } or 
                    // If remit to details are not explicitly mentioned as "Remit to" in the document then keep it empty.
                    "null"
                }```
            """

            # Use the structured text data directly as the user message
            user_message = f"Please analyze this structured document data and extract data:\n\n{structured_text_data}"

            # Prepare messages for Bedrock
            messages = [
                {
                    "role": "user",
                    "content": [{"text": user_message}]
                }
            ]

            self.logger.info("Invoking Bedrock for structured data extraction")



            # Try different Claude models in order of preference
            model_ids = [
                "amazon.nova-pro-v1:0" # "openai.gpt-oss-20b-1:0" #"amazon.nova-pro-v1:0"
            ]

            for model_id in model_ids:
                try:
                    self.logger.info(f"Trying Bedrock model: {model_id}")
                    response = self.bedrock_client.converse(
                        modelId=model_id,
                        system=[{"text": system_prompt_openai}],
                        messages=messages,
                        inferenceConfig={
                            'maxTokens': 1000,
                            'temperature': 0.2,
                            'topP': 0.5,
                        }
                    )
                    self.logger.info(f"Successfully used Bedrock model: {model_id}")
                    break
                except ClientError as model_error:
                    self.logger.warning(f"Model {model_id} failed: {model_error}")
                    if model_id == model_ids[-1]:  # Last model in list
                        # If all models fail, raise the error
                        self.logger.error("All Bedrock models failed")
                        raise
                    continue

            # Extract response text based on model
            content = response['output']['message']['content']
            self.logger.info(f"Response content structure: {content}")

            # Handle different response structures safely
            if model_ids[0] == "openai.gpt-oss-20b-1:0":
                # For OpenAI model, try index 1 first, fallback to index 0
                if len(content) > 1 and 'text' in content[1]:
                    response_text = content[1]['text']
                elif len(content) > 0 and 'text' in content[0]:
                    response_text = content[0]['text']
                else:
                    raise ValueError(f"Unexpected response structure: {content}")
            else:
                # For other models, use index 0
                if len(content) > 0 and 'text' in content[0]:
                    response_text = content[0]['text']
                else:
                    raise ValueError(f"Unexpected response structure: {content}")

            # Parse JSON response
            try:
                extracted_data = json.loads(response_text)
                self.logger.info("Successfully extracted data using Bedrock")
                return extracted_data
            except json.JSONDecodeError:
                self.logger.warning("Bedrock response was not valid JSON, attempting to clean it")
                # Try to extract JSON from response if it contains additional text
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    extracted_data = json.loads(json_match.group())
                    return extracted_data
                else:
                    self._handle_exception(ValueError("Could not parse JSON from Bedrock response"), "parsing Bedrock JSON response", reraise=True)

        except Exception as e:
            self.logger.error(f"Error invoking Bedrock for extraction: {e}")
            raise



    def move_processed_folder(self, file_name: str) -> str:
        """Move the entire folder (with Textract data) to processed location inside extraction folder."""
        try:
            name_without_ext = os.path.splitext(file_name)[0]
            source_folder = os.path.join("data/input_data/extraction", name_without_ext)

            # Create processed folder inside extraction folder if it doesn't exist
            processed_dir = os.path.join("data/input_data/extraction", "processed")
            os.makedirs(processed_dir, exist_ok=True)

            # Destination folder path
            destination_folder = os.path.join(processed_dir, name_without_ext)

            # Move the entire folder
            if os.path.exists(source_folder):
                shutil.move(source_folder, destination_folder)
                self.logger.info(f"Moved processed folder: {source_folder} -> {destination_folder}")
                return destination_folder
            else:
                self.logger.warning(f"Source folder not found: {source_folder}")
                return ""

        except Exception as e:
            self.logger.error(f"Error moving processed folder for {file_name}: {e}")
            raise

    def move_processed_file(self, input_file_path: str) -> str:
        """Move successfully processed file to processed folder."""
        try:
            # Create processed folder if it doesn't exist
            input_dir = os.path.dirname(input_file_path)
            processed_dir = os.path.join(input_dir, "processed")
            os.makedirs(processed_dir, exist_ok=True)

            # Generate destination path
            file_name = os.path.basename(input_file_path)
            destination_path = os.path.join(processed_dir, file_name)

            # Move the file
            shutil.move(input_file_path, destination_path)
            self.logger.info(f"Moved processed file: {input_file_path} -> {destination_path}")

            return destination_path
        except Exception as e:
            self.logger.error(f"Error moving processed file {input_file_path}: {e}")
            raise

    def process_single_file(self, input_file_path: str, output_dir: str = "data/output_data/extraction") -> dict:
        """Process a single file through Textract and Bedrock extraction."""
        try:
            # Generate initial output filename
            file_name = os.path.basename(input_file_path)
            name_without_ext = os.path.splitext(file_name)[0]

            self.logger.info(f"Processing file: {input_file_path}")

            # Step 1: Check if Textract response already exists
            existing_response = self.load_textract_response(file_name)

            if existing_response:
                self.logger.info("Using existing Textract response")
                textract_results = existing_response
            else:
                # Step 2: Upload file to S3
                input_s3_uri = self.upload_file_to_s3(input_file_path)

                # Step 3: Invoke Textract
                textract_response = self.invoke_textract(input_s3_uri)

                # Step 4: Wait for Textract completion and get results
                textract_results = self.check_textract_job_status(textract_response)

                # Step 5: Save Textract response for future use
                self.save_textract_response(textract_results, file_name)

            # Check if processing was successful (either sync or async or existing)
            if existing_response:
                # Using existing response - always successful
                is_successful = True
            else:
                # Check if new processing was successful
                is_successful = (
                    textract_results.get('JobStatus') == 'SUCCEEDED' or  # Async success
                    textract_response.get('is_sync')  # Sync success
                )

            if is_successful:
                # Step 6: Convert Textract response to structured format
                structured_data = self.convert_textract_to_structured_format(textract_results)

                # Step 7: Save Textract data to folder with same name as file
                textract_file_path = self.save_textract_data(structured_data, file_name)

                # Step 8: Use Bedrock to extract specific invoice data
                extracted_invoice_data = self.invoke_bedrock_extraction(structured_data)

                # Step 9: Generate output filename with invoice number (ALWAYS include invoice number)
                invoice_number = extracted_invoice_data.get('invoice_number', '')

                # Clean and validate invoice number
                if invoice_number and str(invoice_number).strip():
                    # Clean invoice number for filename (replace special characters with underscores)
                    clean_invoice_number = re.sub(r'[^\w\-]', '_', str(invoice_number).strip())
                    # Remove multiple consecutive underscores
                    clean_invoice_number = re.sub(r'_+', '_', clean_invoice_number)
                    # Remove leading/trailing underscores
                    clean_invoice_number = clean_invoice_number.strip('_')
                else:
                    # Generate a fallback invoice number if none found
                    import time
                    timestamp = str(int(time.time()))[-6:]  # Last 6 digits of timestamp
                    clean_invoice_number = f"NOINV_{timestamp}"
                    self.logger.warning(f"No invoice number found for {file_name}, using fallback: {clean_invoice_number}")

                # ALWAYS include invoice number in filename
                output_filename = f"{name_without_ext}_{clean_invoice_number}_extracted.json"

                output_file_path = os.path.join(output_dir, output_filename)

                # Check if file already exists and create unique filename if needed
                counter = 1
                original_output_path = output_file_path
                while os.path.exists(output_file_path):
                    base_name = os.path.splitext(original_output_path)[0]
                    output_file_path = f"{base_name}_v{counter}.json"
                    counter += 1

                # Step 10: Save final extracted data (only invoice data)
                os.makedirs(output_dir, exist_ok=True)

                # Save only the extracted invoice data as requested
                with open(output_file_path, 'w') as f:
                    json.dump(extracted_invoice_data, f, indent=2)

                self.logger.info(f"Successfully processed {input_file_path}")
                self.logger.info(f"  - Textract data: {textract_file_path}")
                self.logger.info(f"  - Extracted data: {output_file_path}")

                # Step 10: Move processed file and folder
                try:
                    moved_file_path = self.move_processed_file(input_file_path)
                    moved_folder_path = self.move_processed_folder(file_name)

                    # Step 11: Update tracking CSV with file processing information
                    self.update_tracking_csv(
                        original_pdf_path=input_file_path,
                        moved_pdf_path=moved_file_path,
                        extracted_json_path=output_file_path,
                        invoice_number=extracted_invoice_data.get('invoice_number', ''),
                        vendor_name=extracted_invoice_data.get('vendor_name', ''),
                        processing_status='success'
                    )

                    return {
                        'status': 'success',
                        'input_file': input_file_path,
                        'moved_file_to': moved_file_path,
                        'moved_folder_to': moved_folder_path,
                        'textract_data_file': textract_file_path,
                        'output_file': output_file_path,
                        'extracted_data': extracted_invoice_data
                    }
                except Exception as move_error:
                    self.logger.warning(f"File processed successfully but failed to move: {move_error}")

                    # Update tracking CSV even if move failed
                    self.update_tracking_csv(
                        original_pdf_path=input_file_path,
                        moved_pdf_path=input_file_path,  # Use original path if move failed
                        extracted_json_path=output_file_path,
                        invoice_number=extracted_invoice_data.get('invoice_number', ''),
                        vendor_name=extracted_invoice_data.get('vendor_name', ''),
                        processing_status='success_move_failed'
                    )

                    return {
                        'status': 'success',
                        'input_file': input_file_path,
                        'textract_data_file': textract_file_path,
                        'output_file': output_file_path,
                        'extracted_data': extracted_invoice_data,
                        'move_error': str(move_error)
                    }

            else:
                error_msg = f"Textract job failed with status: {textract_results.get('JobStatus', 'Unknown')}"
                self.logger.error(f"Textract processing failed for {input_file_path}: {error_msg}")

                # Update tracking CSV for failed processing
                self.update_tracking_csv(
                    original_pdf_path=input_file_path,
                    moved_pdf_path='',
                    extracted_json_path='',
                    invoice_number='',
                    vendor_name='',
                    processing_status=f'failed: {error_msg}'
                )

                return {
                    'status': 'failed',
                    'input_file': input_file_path,
                    'error': error_msg
                }

        except Exception as e:
            # Log the error but let it bubble up for debugger to catch
            self.logger.error(f"Error processing file {input_file_path}: {e}")

            # Update tracking CSV for exception
            try:
                self.update_tracking_csv(
                    original_pdf_path=input_file_path,
                    moved_pdf_path='',
                    extracted_json_path='',
                    invoice_number='',
                    vendor_name='',
                    processing_status=f'exception: {str(e)}'
                )
            except:
                pass  # Don't let tracking errors interfere with main exception

            # Re-raise the exception so VS Code debugger can catch it
            raise

    def process_multiple_files(self, input_folder: str, max_files: Optional[int] = None, output_dir: str = "data/output_data/extraction", max_workers: int = 4) -> list:
        """Process multiple files from all subfolders (1 level deep) excluding 'processed' folders."""
        # Get all supported file types (PDF, images, etc.)
        supported_extensions = ['*.pdf', '*.png', '*.jpg', '*.jpeg', '*.tiff', '*.bmp']
        input_files = []

        self.logger.info(f"Scanning for files in main folder and all subfolders (1 level deep): {input_folder}")

        # First, get files from the main folder
        for ext in supported_extensions:
            input_files.extend(glob.glob(os.path.join(input_folder, ext)))
            input_files.extend(glob.glob(os.path.join(input_folder, ext.upper())))

        # Then, get files from all subfolders (1 level deep)
        try:
            for item in os.listdir(input_folder):
                item_path = os.path.join(input_folder, item)

                # Check if it's a directory and not named "processed"
                if os.path.isdir(item_path) and item.lower() != "processed":
                    self.logger.info(f"Scanning subfolder: {item}")

                    # Get files from this subfolder
                    for ext in supported_extensions:
                        subfolder_files = glob.glob(os.path.join(item_path, ext))
                        subfolder_files.extend(glob.glob(os.path.join(item_path, ext.upper())))
                        input_files.extend(subfolder_files)

                        if subfolder_files:
                            self.logger.info(f"Found {len(subfolder_files)} {ext} files in {item}")
                elif item.lower() == "processed":
                    self.logger.info(f"Skipping 'processed' folder: {item}")

        except OSError as e:
            self.logger.error(f"Error scanning directory {input_folder}: {e}")
            return []

        if not input_files:
            self.logger.warning(f"No supported files found in {input_folder} or its subfolders")
            return []

        # Remove duplicates and sort for consistent processing order
        input_files = sorted(list(set(input_files)))
        self.logger.info(f"Total files found: {len(input_files)}")

        # Limit number of files if specified
        if max_files and max_files > 0:
            input_files = input_files[:max_files]
            self.logger.info(f"Processing {len(input_files)} files (limited to {max_files})")
        else:
            self.logger.info(f"Processing all {len(input_files)} files found")

        # Print list of files to be processed
        self.logger.info("Files to be processed:")
        for i, file_path in enumerate(input_files, 1):
            self.logger.info(f"  {i}. {file_path}")

        # Process files in parallel for much faster processing
        results = []
        successful_count = 0
        failed_count = 0

        def process_file_wrapper(file_info):
            """Wrapper function for parallel processing."""
            i, file_path = file_info
            try:
                self.logger.info(f"Processing file {i}/{len(input_files)}: {os.path.basename(file_path)}")
                result = self.process_single_file(file_path, output_dir)

                # Add file index for tracking
                result['file_index'] = i
                result['file_name'] = os.path.basename(file_path)

                return result
            except Exception as e:
                error_msg = f"Exception during processing: {str(e)}"
                self.logger.error(f"❌ EXCEPTION: {os.path.basename(file_path)} - {error_msg}")
                return {
                    'status': 'failed',
                    'input_file': file_path,
                    'error': error_msg,
                    'file_index': i,
                    'file_name': os.path.basename(file_path)
                }

        # Use ThreadPoolExecutor for parallel processing
        self.logger.info(f"Starting parallel processing with {max_workers} workers")
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all files for processing
            file_infos = [(i, file_path) for i, file_path in enumerate(input_files, 1)]
            future_to_file = {executor.submit(process_file_wrapper, file_info): file_info for file_info in file_infos}

            # Process completed results as they finish
            for future in concurrent.futures.as_completed(future_to_file):
                file_info = future_to_file[future]
                i, file_path = file_info

                try:
                    result = future.result()
                    results.append(result)

                    # Print and log detailed results for each file
                    if result['status'] == 'success':
                        successful_count += 1
                        self.logger.info(f"✅ SUCCESS: {result['file_name']}")
                        self.logger.info(f"   Textract data: {result.get('textract_data_file', 'N/A')}")
                        self.logger.info(f"   Output file: {result.get('output_file', 'N/A')}")

                        # Print extracted data to terminal and log
                        extracted_data = result.get('extracted_data', {})
                        self.logger.info("   Extracted Data:")
                        for key, value in extracted_data.items():
                            self.logger.info(f"     {key}: {value}")

                        print(f"\n✅ File {result['file_index']}/{len(input_files)} - SUCCESS: {result['file_name']}")
                        print("Extracted Data:")
                        print(json.dumps(extracted_data, indent=2))

                    else:
                        failed_count += 1
                        error_msg = result.get('error', 'Unknown error')
                        self.logger.error(f"❌ FAILED: {result['file_name']} - {error_msg}")
                        print(f"\n❌ File {result['file_index']}/{len(input_files)} - FAILED: {result['file_name']}")
                        print(f"Error: {error_msg}")

                except Exception as e:
                    failed_count += 1
                    error_msg = f"Future exception: {str(e)}"
                    self.logger.error(f"❌ FUTURE EXCEPTION: {os.path.basename(file_path)} - {error_msg}")
                    print(f"\n❌ File {i}/{len(input_files)} - FUTURE EXCEPTION: {os.path.basename(file_path)}")
                    print(f"Error: {error_msg}")

                    results.append({
                        'status': 'failed',
                        'input_file': file_path,
                        'error': error_msg,
                        'file_index': i,
                        'file_name': os.path.basename(file_path)
                    })

        # Print and log final summary
        self.logger.info(f"\n{'='*80}")
        self.logger.info("PROCESSING SUMMARY")
        self.logger.info(f"{'='*80}")
        self.logger.info(f"Total files processed: {len(results)}")
        self.logger.info(f"Successful: {successful_count}")
        self.logger.info(f"Failed: {failed_count}")
        self.logger.info(f"Success rate: {(successful_count/len(results)*100):.1f}%" if results else "0%")

        print(f"\n{'='*80}")
        print("FINAL PROCESSING SUMMARY")
        print(f"{'='*80}")
        print(f"Total files processed: {len(results)}")
        print(f"Successful: {successful_count}")
        print(f"Failed: {failed_count}")
        print(f"Success rate: {(successful_count/len(results)*100):.1f}%" if results else "0%")

        return results

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Textract + Bedrock Document Processing')
    parser.add_argument('--input-folder', '-i',
                       help='Path to folder containing input files to process')
    parser.add_argument('--max-files', '-m', type=int, default=None,
                       help='Maximum number of files to process (default: process all)')
    parser.add_argument('--output-dir', '-o', default='data/output_data/extraction',
                       help='Output directory for processed files (default: data/output_data/extraction)')
    parser.add_argument('--single-file', '-s',
                       help='Process a single file instead of a folder')
    parser.add_argument('--max-workers', '-w', type=int, default=4,
                       help='Maximum number of parallel workers for processing (default: 4)')

    # Check if running from VS Code or without arguments
    import sys
    if len(sys.argv) == 1:
        # No arguments provided - use defaults for VS Code execution
        print("No arguments provided. Using default settings for VS Code execution:")
        print("  - Processing 1 file from data/input_data/extraction folder")
        print("  - Output will be saved to data/output_data/extraction")
        print("  - Use --help to see all available options")
        print()

        # Create a mock args object with default values
        class DefaultArgs:
            def __init__(self):
                self.input_folder = 'data/input_data/extraction'
                self.max_files = 20  # Process only 1 file by default
                self.output_dir = 'data/output_data/extraction'
                self.single_file = None
                self.max_workers = 4  # Default parallel workers

        return DefaultArgs()

    args = parser.parse_args()

    # Validate that either input-folder or single-file is provided
    if not args.input_folder and not args.single_file:
        parser.error("Either --input-folder or --single-file must be provided")

    return args

def main():
    # Parse command line arguments
    args = parse_arguments()

    # Setup logging
    logger = setup_logging()
    logger.info("Starting Textract + Bedrock document processing")

    # Configuration
    AWS_REGION = 'us-east-1'
    BUCKET_NAME = 'document-extraction-logistically'  # Replace with your S3 bucket
    TEMP_INPUT_PREFIX = 'temp/textract_input'  # Temporary S3 input path
    TEMP_OUTPUT_PREFIX = 'temp/textract_output'  # Temporary S3 output path

    # Initialize TextractBedrockAutomation client with debug mode enabled
    processor = TextractBedrockAutomation(
        region=AWS_REGION,
        bucket_name=BUCKET_NAME,
        temp_input_prefix=TEMP_INPUT_PREFIX,
        temp_output_prefix=TEMP_OUTPUT_PREFIX,
        logger=logger,
        debug_mode=False  # Enable debug mode to stop at exceptions
    )

    try:
        if args.single_file:
            # Process single file
            if not os.path.exists(args.single_file):
                logger.error(f"Input file {args.single_file} not found.")
                return

            logger.info(f"Processing single file: {args.single_file}")
            result = processor.process_single_file(args.single_file, args.output_dir)

            if result['status'] == 'success':
                logger.info("Processing completed successfully!")
                logger.info(f"Textract data saved to: {result['textract_data_file']}")
                logger.info(f"Extracted data saved to: {result['output_file']}")
                print("\nExtracted Invoice Data:")
                print(json.dumps(result['extracted_data'], indent=2))
            else:
                logger.error(f"Processing failed: {result.get('error', 'Unknown error')}")

        else:
            # Process multiple files from folder
            if not os.path.exists(args.input_folder):
                logger.error(f"Input folder {args.input_folder} not found.")
                return

            logger.info(f"Processing files from folder: {args.input_folder}")
            logger.info(f"Using {args.max_workers} parallel workers for faster processing")
            results = processor.process_multiple_files(args.input_folder, args.max_files, args.output_dir, args.max_workers)

            # Summary
            successful = sum(1 for r in results if r['status'] == 'success')
            failed = len(results) - successful

            logger.info(f"\nProcessing Summary:")
            logger.info(f"Total files processed: {len(results)}")
            logger.info(f"Successful: {successful}")
            logger.info(f"Failed: {failed}")

            if failed > 0:
                logger.info("\nFailed files:")
                for result in results:
                    if result['status'] != 'success':
                        logger.error(f"  {result['input_file']}: {result.get('error', 'Unknown error')}")

            # Show sample extracted data from successful files
            successful_results = [r for r in results if r['status'] == 'success']
            if successful_results:
                logger.info("\nSample extracted data from first successful file:")
                print(json.dumps(successful_results[0]['extracted_data'], indent=2))

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise

# adding second main function to process single file
def process_single_file(file_path):
    # Setup logging
    logger = setup_logging()
    logger.info(f"Processing single file: {file_path}")

    # Configuration
    AWS_REGION = 'us-east-1'
    BUCKET_NAME = 'document-extraction-logistically'  # Replace with your S3 bucket
    TEMP_INPUT_PREFIX = 'temp/textract_input'  # Temporary S3 input path
    TEMP_OUTPUT_PREFIX = 'temp/textract_output'  # Temporary S3 output path

    # Initialize TextractBedrockAutomation client with debug mode enabled
    processor = TextractBedrockAutomation(
        region=AWS_REGION,
        bucket_name=BUCKET_NAME,
        temp_input_prefix=TEMP_INPUT_PREFIX,
        temp_output_prefix=TEMP_OUTPUT_PREFIX,
        logger=logger,
        debug_mode=True  # Enable debug mode to stop at exceptions
    )

    try:
        result = processor.process_single_file(file_path)

        if result['status'] == 'success':
            logger.info("Processing completed successfully!")
            logger.info(f"Textract data saved to: {result['textract_data_file']}")
            logger.info(f"Extracted data saved to: {result['output_file']}")
            print("\nExtracted Invoice Data:")
            print(json.dumps(result['extracted_data'], indent=4))
        else:
            logger.error(f"Processing failed: {result.get('error', 'Unknown error')}")

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise

if __name__ == "__main__":

    #process_single_file("/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/invoice_shortlisted/Dohrn Transfer/MSINTL11325563_carrier_invoice.pdf")

    # processing multiple files inside "/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/invoice_shortlisted" folder

        # Setup logging
    logger = setup_logging()
    logger.info("Starting Textract + Bedrock document processing")

    # Configuration
    AWS_REGION = 'us-east-1'
    BUCKET_NAME = 'document-extraction-logistically'  # Replace with your S3 bucket
    TEMP_INPUT_PREFIX = 'temp/textract_input'  # Temporary S3 input path
    TEMP_OUTPUT_PREFIX = 'temp/textract_output'  # Temporary S3 output path
    INPUT_FOLDER = '/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/failed_in_shortlisted_v2'

    processor = TextractBedrockAutomation(
        region=AWS_REGION,
        bucket_name=BUCKET_NAME,
        temp_input_prefix=TEMP_INPUT_PREFIX,
        temp_output_prefix=TEMP_OUTPUT_PREFIX,
        logger=logger,
        debug_mode=False  # Enable debug mode to stop at exceptions
    )

    results = processor.process_multiple_files(INPUT_FOLDER, max_files=50, output_dir="data/output_data/extraction", max_workers=4)
    
    # INPUT_FILE = r"/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/failed_in_shortlisted_v2/10471969.pdf"
    # results = processor.process_single_file(INPUT_FILE)

    # command to process single file
    # python3 app/app_ext_textract+llm.py --single-file /home/<USER>/Documents/repositories/logistically/data/input_data/extraction/Southeastern_Freight_Lines-selected/HMFL11418924_INV.pdf

    # command to process whole folder
    # python3 app/app_extraction_textract_and_llm.py --input-folder /home/<USER>/Documents/repositories/logistically/data/input_data/extraction/Performance Freight Systems-selected