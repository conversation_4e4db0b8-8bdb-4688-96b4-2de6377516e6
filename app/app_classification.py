#!/usr/bin/env python3
"""
PDF Textract Bedrock Processor

This script:
1. Takes a PDF file path as input
2. Splits the PDF into individual pages
3. Processes each page with AWS Textract (sync method) in parallel
4. Merges all page results with <page1></page1> tags
5. Uses AWS Bedrock Converse API with OpenAI models to analyze document types

Usage:
    python pdf_textract_bedrock_processor.py /path/to/document.pdf
"""

import os
import sys
import json
import logging
import tempfile
import concurrent.futures
import csv
import shutil
import asyncio
import time
import uuid
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional

import boto3
import PyPDF2
from botocore.exceptions import ClientError


def setup_logging() -> logging.Logger:
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def split_pdf(pdf_path: str, output_dir: str) -> List[str]:
    """
    Split PDF into individual pages and save them as separate PDF files.

    Args:
        pdf_path: Path to the input PDF file
        output_dir: Directory to save individual page PDFs

    Returns:
        List of paths to individual page PDF files
    """
    page_files = []

    # Create unique identifier for this PDF to avoid conflicts
    pdf_hash = hashlib.md5(pdf_path.encode()).hexdigest()[:8]
    pdf_name = Path(pdf_path).stem

    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        total_pages = len(pdf_reader.pages)

        logging.info(f"Splitting PDF {pdf_name} into {total_pages} pages")

        for page_num in range(total_pages):
            # Create a new PDF writer for each page
            pdf_writer = PyPDF2.PdfWriter()
            pdf_writer.add_page(pdf_reader.pages[page_num])

            # Save individual page with unique naming to prevent conflicts
            page_filename = f"{pdf_name}_{pdf_hash}_page_{page_num + 1:03d}.pdf"
            page_path = os.path.join(output_dir, page_filename)

            with open(page_path, 'wb') as output_file:
                pdf_writer.write(output_file)

            page_files.append(page_path)
            logging.debug(f"Created page file: {page_filename}")

    return page_files


def process_page_with_textract(page_path: str, s3_client, textract_client, bucket_name: str, temp_prefix: str) -> Tuple[int, str]:
    """
    Process a single PDF page with AWS Textract (sync method with async fallback).

    Args:
        page_path: Path to the PDF page file
        s3_client: AWS S3 client
        textract_client: AWS Textract client
        bucket_name: S3 bucket name for temporary uploads
        temp_prefix: S3 prefix for temporary files

    Returns:
        Tuple of (page_number, extracted_text)
    """
    try:
        # Extract page number from filename (handle new format: pdfname_hash_page_001.pdf)
        page_filename = os.path.basename(page_path)

        # Parse filename to extract page number safely
        if '_page_' in page_filename:
            # New format: pdfname_hash_page_001.pdf
            page_part = page_filename.split('_page_')[1].split('.')[0]
            page_num = int(page_part)
        else:
            # Fallback for old format: page_001.pdf
            page_num = int(page_filename.split('_')[1].split('.')[0])

        logging.debug(f"Processing page {page_num} from file: {page_filename}")

        s3_key = None  # Track if we uploaded to S3

        try:
            # Try sync method first (faster for single pages) - use direct file input
            with open(page_path, 'rb') as document_file:
                response = textract_client.detect_document_text(
                    Document={'Bytes': document_file.read()}
                )
            logging.debug(f"Sync method succeeded for page {page_num}")

        except Exception as sync_error:
            logging.warning(f"Sync method failed for page {page_num}, trying async method: {sync_error}")

            # Upload to S3 only when async method is needed with unique key
            timestamp = int(time.time() * 1000)  # millisecond timestamp
            unique_id = uuid.uuid4().hex[:8]
            s3_key = f"{temp_prefix}/{timestamp}_{unique_id}_{page_filename}"
            s3_client.upload_file(page_path, bucket_name, s3_key)
            logging.info(f"Uploaded {page_filename} to S3 with unique key: {s3_key}")

            # Fallback to async method
            try:
                # Start async job
                start_response = textract_client.start_document_text_detection(
                    DocumentLocation={
                        'S3Object': {
                            'Bucket': bucket_name,
                            'Name': s3_key
                        }
                    }
                )

                job_id = start_response['JobId']
                logging.info(f"Started async job {job_id} for page {page_num}")

                # Poll for completion
                max_wait_time = 300  # 5 minutes max wait
                poll_interval = 2    # Poll every 2 seconds
                elapsed_time = 0

                while elapsed_time < max_wait_time:
                    time.sleep(poll_interval)
                    elapsed_time += poll_interval

                    result_response = textract_client.get_document_text_detection(JobId=job_id)
                    status = result_response['JobStatus']

                    if status == 'SUCCEEDED':
                        response = result_response
                        logging.info(f"Async method succeeded for page {page_num}")
                        break
                    elif status == 'FAILED':
                        raise Exception(f"Async job failed: {result_response.get('StatusMessage', 'Unknown error')}")
                    elif status in ['IN_PROGRESS']:
                        logging.debug(f"Async job {job_id} still in progress...")
                        continue
                    else:
                        raise Exception(f"Unexpected job status: {status}")

                if elapsed_time >= max_wait_time:
                    raise Exception(f"Async job timed out after {max_wait_time} seconds")

            except Exception as async_error:
                logging.error(f"Both sync and async methods failed for page {page_num}: {async_error}")
                raise async_error

        # Extract text from response
        text_lines = []
        for block in response.get('Blocks', []):
            if block['BlockType'] == 'LINE':
                text_lines.append(block.get('Text', ''))

        extracted_text = '\n'.join(text_lines)

        # Add validation and metadata for debugging
        char_count = len(extracted_text)
        line_count = len(text_lines)

        logging.info(f"Page {page_num}: Extracted {char_count} characters, {line_count} lines from {page_filename}")

        # Add page metadata to help with debugging
        page_metadata = f"[PAGE_METADATA: file={page_filename}, page={page_num}, chars={char_count}, lines={line_count}]"
        # validated_text = f"{page_metadata}\n{extracted_text}"
        validated_text = f"{extracted_text}"

        # Clean up S3 file only if it was uploaded
        if s3_key:
            try:
                s3_client.delete_object(Bucket=bucket_name, Key=s3_key)
                logging.debug(f"Cleaned up S3 file: {s3_key}")
            except Exception:
                pass  # Ignore cleanup errors

        return page_num, validated_text

    except Exception as e:
        logging.error(f"Error processing page {page_path}: {e}")
        return 0, f"Error processing page: {e}"


def process_pdf_pages_parallel(page_files: List[str], bucket_name: str, temp_prefix: str = "temp-textract", max_workers: int = 5) -> str:
    """
    Process multiple PDF pages with Textract in parallel with enhanced validation.

    Args:
        page_files: List of paths to individual page PDF files
        bucket_name: S3 bucket name for temporary uploads
        temp_prefix: S3 prefix for temporary files
        max_workers: Maximum number of parallel workers

    Returns:
        Combined text with page tags
    """
    # Create unique temp prefix for this processing session
    session_id = uuid.uuid4().hex[:8]
    unique_temp_prefix = f"{temp_prefix}-{session_id}"

    logging.info(f"Starting parallel processing of {len(page_files)} pages with session ID: {session_id}")

    # Initialize AWS clients
    s3_client = boto3.client('s3')
    textract_client = boto3.client('textract')

    # Process pages in parallel
    page_results = {}
    expected_pages = set()

    # Extract expected page numbers from filenames
    for page_file in page_files:
        filename = os.path.basename(page_file)
        if '_page_' in filename:
            page_part = filename.split('_page_')[1].split('.')[0]
            expected_pages.add(int(page_part))
        else:
            page_part = filename.split('_')[1].split('.')[0]
            expected_pages.add(int(page_part))

    logging.info(f"Expected pages: {sorted(expected_pages)}")

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_page = {
            executor.submit(process_page_with_textract, page_path, s3_client, textract_client, bucket_name, unique_temp_prefix): page_path
            for page_path in page_files
        }

        # Collect results
        for future in concurrent.futures.as_completed(future_to_page):
            page_path = future_to_page[future]
            try:
                page_num, extracted_text = future.result()

                # Validate page number is expected
                if page_num not in expected_pages:
                    logging.warning(f"Unexpected page number {page_num} from {page_path}. Expected: {sorted(expected_pages)}")

                # Check for duplicate page results
                if page_num in page_results:
                    logging.error(f"Duplicate page {page_num} detected! Previous: {len(page_results[page_num])} chars, New: {len(extracted_text)} chars")
                    # Keep the longer text (likely more complete)
                    if len(extracted_text) > len(page_results[page_num]):
                        page_results[page_num] = extracted_text
                        logging.info(f"Replaced page {page_num} with longer text")
                else:
                    page_results[page_num] = extracted_text

                logging.info(f"Completed processing page {page_num} from {os.path.basename(page_path)}")
            except Exception as e:
                logging.error(f"Error processing {page_path}: {e}")

    # Validate all expected pages were processed
    processed_pages = set(page_results.keys()) - {0}  # Remove error pages
    missing_pages = expected_pages - processed_pages
    extra_pages = processed_pages - expected_pages

    if missing_pages:
        logging.error(f"Missing pages: {sorted(missing_pages)}")
    if extra_pages:
        logging.warning(f"Extra pages: {sorted(extra_pages)}")

    # Combine results in page order
    combined_text_parts = []
    for page_num in sorted(page_results.keys()):
        if page_num > 0:  # Skip error pages (page_num = 0)
            page_text = page_results[page_num]
            combined_text_parts.append(f"<page{page_num}>\n{page_text}\n</page{page_num}>")

    logging.info(f"Combined {len(combined_text_parts)} pages into final text")
    return '\n\n'.join(combined_text_parts)


def validate_and_clean_extracted_text(combined_text: str, pdf_path: str) -> str:
    """
    Validate and clean the extracted text to ensure data integrity.

    Args:
        combined_text: Combined text from all pages
        pdf_path: Original PDF path for validation

    Returns:
        Cleaned and validated text
    """
    pdf_name = Path(pdf_path).stem

    # Split into pages
    pages = combined_text.split('<page')
    cleaned_pages = []

    for i, page_content in enumerate(pages):
        if not page_content.strip():
            continue

        # Extract page number and content
        if '>' in page_content:
            page_header, content = page_content.split('>', 1)
            if '</page' in content:
                content = content.split('</page')[0]

            # Validate page metadata if present
            if '[PAGE_METADATA:' in content:
                metadata_end = content.find(']')
                if metadata_end != -1:
                    metadata = content[:metadata_end + 1]
                    actual_content = content[metadata_end + 1:].strip()

                    # Check if metadata matches expected PDF
                    if f'file={pdf_name}' in metadata or pdf_name in metadata:
                        cleaned_pages.append(f'<page{page_header}>\n{content}\n</page{page_header.split(">")[0]}>')
                        logging.debug(f"Validated page {i} metadata for {pdf_name}")
                    else:
                        logging.warning(f"Page {i} metadata mismatch for {pdf_name}: {metadata}")
                        # Still include but flag for review
                        cleaned_pages.append(f'<page{page_header}>\n[VALIDATION_WARNING: Metadata mismatch]\n{content}\n</page{page_header.split(">")[0]}>')
                else:
                    cleaned_pages.append(f'<page{page_header}>\n{content}\n</page{page_header.split(">")[0]}>')
            else:
                cleaned_pages.append(f'<page{page_header}>\n{content}\n</page{page_header.split(">")[0]}>')

    cleaned_text = '\n\n'.join(cleaned_pages)

    # Log validation summary
    original_pages = len([p for p in pages if p.strip()])
    cleaned_page_count = len(cleaned_pages)

    logging.info(f"Text validation for {pdf_name}: {original_pages} -> {cleaned_page_count} pages")

    return cleaned_text


def analyze_document_types_with_bedrock(combined_text: str, region: str = 'us-east-1', reasoning_effort="medium") -> Tuple[Dict, Dict]:
    """
    Analyze document types using AWS Bedrock Converse API with OpenAI models.

    Args:
        combined_text: Combined text from all pages with page tags
        region: AWS region for Bedrock

    Returns:
        Tuple containing (analysis_result, full_bedrock_response)
    """
    bedrock_client = boto3.client('bedrock-runtime', region_name='us-west-2') # region)
    
    # System prompt for document type analysis
    system_prompt = '''
                ## Task Summary:
                    You are an expert document classification AI specialized in classifying logistics documents. You strictly follow these rules and output only via the provided tool call `extract_logistics_doc_type`.

                ## Model Instructions:
                    - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.
                    - Use **only** the `extract_logistics_doc_type` tool to return results.
                    - For every page in the input PDF you MUST return exactly one object describing that page.
                    - `doc_type` must be one of the exact enum strings listed below (lowercase, underscore where shown).
                    - Do NOT return any extra pages or skip pages. Output pages in ascending page order.
                    - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).
                    - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.
                    - Strickly check for contination of previous page by checking if the page starts with any of the following: "continued", "continued on next page", "continued on next", etc. or page 2 of 3, etc.

                ## Enum details for doc_type:

                    invoice — Carrier Invoice
                    Definition: Bill issued by a carrier for services rendered (generic carrier billing).
                    Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.
                    Key fields/structure: carrier billing address, shipment ID, line charges, total due.

                    bol — Bill of Lading
                    Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.
                    Keywords indication: "Bill of Lading", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading
                    Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.

                    pod — Proof of Delivery
                    Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.
                    Keywords indication: "Proof of Delivery", "Delivery Ticket", POD, Received by, Delivered, Delivery Receipt, Date Received
                    Key fields/structure: signature/date block, delivery address, condition remarks, signature line.

                    rate_confirmation — Carrier Rate Confirmation
                    Definition: Agreement from a carrier confirming rate/terms for a specific load.
                    Keywords indication: "Carrier Rate Confirmation", Carrier Rate, Rate Confirmation, Carrier:
                    Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.

                    cust_rate_confirmation — Customer Rate Confirmation
                    Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.
                    Keywords indication: "Customer Rate Confirmation", Customer Rate, Quote to
                    Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.

                    clear_to_pay — Clear to Pay
                    Definition: Authorization indicating invoice is approved for payment.
                    Keywords indication: "Clear to Pay", Approved for Payment, Payment Authorization, Clear to Pay Stamp
                    Key fields/structure: approval stamps, audit/verification notes, approver name/date.

                    scale_ticket — Scale Ticket
                    Definition: Weight record from a scale for vehicle/load (used for billing/compliance).
                    Keywords indication: "Scale Ticket", Gross Weight, Tare, Net Weight, Weighed At
                    Key fields/structure: gross/tare/net values, scale location, date/time, truck ID.

                    freight_bill — Freight Bill
                    Definition: Carrier’s invoice detailing charges for transport (carrier-issued billing).
                    Keywords indication: "Freight Bill", Freight Charges, Carrier Invoice, Bill To (carrier name)
                    Key fields/structure: carrier billing block, shipment refs, line-item charges, total due.

                    log — Log
                    Definition: Activity record (driver log, tracking log) for operational/regulatory use.
                    Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp
                    Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.

                    fuel_receipt — Fuel Receipt
                    Definition: Receipt for fuel purchase (expense item).
                    Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt
                    Key fields/structure: fuel quantity, unit price, station name/address, payment method.

                    cust_invoice — Customer Invoice
                    Definition: Invoice directed to a customer for logistics services (customer-facing billing).
                    Keywords indication: Invoice to, Invoice (Customer), Bill To: Customer, Customer Invoice
                    Key fields/structure: customer billing block, service descriptions, total due from customer.

                    combined_carrier_documents — Combined Carrier Documents
                    Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).
                    Keywords indication: multiple distinct document headers on one page, Bundle, Combined
                    Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.

                    pack_list — Packing List
                    Definition: Itemized list of shipment contents for inventory/receiving checks.
                    Keywords indication: Packing List, Pack List, Contents, Qty, Item Description
                    Key fields/structure: SKU/description rows, package counts, net/gross units.

                    po — Purchase Order
                    Definition: Buyer’s order to a seller specifying items, qty, and price.
                    Keywords indication: Purchase Order, PO#, Buyer, PO Number
                    Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.

                    comm_invoice — Commercial Invoice
                    Definition: Customs-focused invoice for international shipments (value, HS codes).
                    Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value
                    Key fields/structure: declared value, HS codes, importer/exporter details.

                    customs_doc — Customs Document
                    Definition: General customs paperwork (declarations, certificates, permits).
                    Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker
                    Key fields/structure: declaration forms, license numbers, broker stamps.

                    nmfc_cert — NMFC Certificate
                    Definition: Document showing National Motor Freight Classification codes/class assignments.
                    Keywords indication: NMFC, National Motor Freight Classification, Class
                    Key fields/structure: NMFC codes, class #, commodity description.

                    other — Other
                    Definition: Any logistics-related page that doesn’t fit the above categories. Use sparingly.
                    Keywords indication: none specific.
                    Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.

                    coa — Certificate of Analysis
                    Definition: Lab-quality report verifying composition/quality of goods.
                    Keywords indication: Certificate of Analysis, COA, Test Results, Specifications
                    Key fields/structure: analyte/test results, batch/lot numbers, lab stamp/signature.

                    tender_from_cust — Load Tender from Customer
                    Definition: Customer’s load tender or request to a carrier to transport a load.
                    Keywords indication: Load Tender, Tender, Tender from, Request to Carrier
                    Key fields/structure: tender number, pickup/delivery instructions, special requirements.

                    lumper_receipt — Lumper Receipt
                    Definition: Receipt for lumper services (loading/unloading labor).
                    Keywords indication: Lumper, Lumper Receipt, Lumper Charge
                    Key fields/structure: labor charge, lumper name/signature, location/date.

                    so_confirmation — Sales Order Confirmation
                    Definition: Seller’s confirmation of a sales order (acknowledges order details).
                    Keywords indication: Sales Order Confirmation, SO#, Order Confirmation
                    Key fields/structure: SO number, confirmed quantities/dates/prices.

                    po_confirmation — Purchase Order Confirmation
                    Definition: Seller’s acceptance/confirmation of a buyer’s PO.
                    Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement
                    Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.

                    pre_pull_receipt — Pre-Pull Receipt
                    Definition: Proof/receipt for pre-pulling a container or load before scheduled pickup.
                    Keywords indication: Pre-pull, Pre Pull, Prepull, Pre-Pull Receipt
                    Key fields/structure: container ID, pull date/time, storage/yard reference, fees.

                    ingate — Ingate Document
                    Definition: Record of vehicle/container entering a facility (gate-in).
                    Keywords indication: Ingate, Gate In, Time In, Truck In, Entry Time
                    Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.

                    outgate — Outgate Document
                    Definition: Record of vehicle/container exiting a facility (gate-out/release).
                    Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time
                    Key fields/structure: release stamp, exit time, fees, container/truck number.
            '''

    # User message with the combined text
    user_message = f"Please analyze this multi-page document and classify each page:\n\n{combined_text}"
    
    # Prepare messages for Bedrock
    messages = [
        {
            "role": "user", 
            "content": [{"text": user_message}]
        }
    ]
    
    # Try models available on Bedrock (using inference profiles and available models) openai oss
    model_id = "openai.gpt-oss-20b-1:0" # "amazon.nova-pro-v1:0"

    # Define the tool configuration with enum and schema for page details
    tool_config = {
        "tools": [
            {
                "toolSpec": {
                    "name": "classify_logistics_doc_type",
                    "description": "Classify logistics document type from multi-page documents",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "documents": {
                                    "type": "array",
                                    "description": "Array of extracted document type summaries from the multi-page document",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "page_no": {
                                                "type": "integer",
                                                "description": "Total number of pages for this document type"
                                            },
                                            "doc_type": {
                                                "type": "string",
                                                "enum": [
                                                    "bol", "pod", "rate_confirmation", "cust_rate_confirmation",
                                                    "clear_to_pay", "scale_ticket", "freight_bill", "log",
                                                    "fuel_receipt", "invoice", "cust_invoice",
                                                    "combined_carrier_documents", "pack_list", "po",
                                                    "comm_invoice", "customs_doc", "nmfc_cert", "other",
                                                    "coa", "tender_from_cust", "lumper_receipt",
                                                    "so_confirmatin", "po_confirmatin", "pre_pull_receipt",
                                                    "ingate", "outgate"
                                                ],
                                                "description": "For detailed description of each enum, refer to the system prompt"
                                            }
                                        },
                                        "required": ["page_no", "doc_type"]
                                    }
                                }
                            },
                            "required": ["documents"]
                        }
                    }
                }
            }
        ]
}




    logging.info(f"Trying Bedrock model: {model_id}")
    response = bedrock_client.converse(
            modelId=model_id,
            system=[{"text": system_prompt}],
            messages=messages,
            toolConfig=tool_config,
            inferenceConfig={
                'temperature': 0.7
            },
            additionalModelRequestFields={
                "reasoning_effort": reasoning_effort  # "low", "medium", or "high"
            }

        )


    # Extract response text
    content = response['output']['message']['content'][1]['toolUse']['input']
    return content, response


def update_tracking_csv(pdf_path: str, status: str, metadata: Optional[Dict] = None) -> None:
    """
    Update tracking CSV file with processing status and metadata for each PDF.

    Args:
        pdf_path: Path to the PDF file being processed
        status: Processing status (e.g., 'started', 'completed', 'failed')
        metadata: Additional metadata to track
    """
    tracking_file = Path("data/processing_tracking.csv")

    # Ensure data directory exists
    tracking_file.parent.mkdir(parents=True, exist_ok=True)

    # Check if file exists to determine if we need headers
    file_exists = tracking_file.exists()

    # Prepare row data
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    pdf_name = Path(pdf_path).name
    pdf_size = os.path.getsize(pdf_path) if os.path.exists(pdf_path) else 0

    row_data = {
        'timestamp': timestamp,
        'pdf_name': pdf_name,
        'pdf_path': pdf_path,
        'status': status,
        'file_size_bytes': pdf_size
    }

    # Add metadata if provided
    if metadata:
        row_data.update(metadata)

    # Write to CSV
    with open(tracking_file, 'a', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['timestamp', 'pdf_name', 'pdf_path', 'status', 'file_size_bytes']

        # Add any additional fields from metadata
        if metadata:
            fieldnames.extend([key for key in metadata.keys() if key not in fieldnames])

        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # Write header if file is new
        if not file_exists:
            writer.writeheader()

        writer.writerow(row_data)


def save_textract_response(pdf_path: str, combined_text: str) -> str:
    """
    Save combined Textract response text to a file in the same directory as the PDF.

    Args:
        pdf_path: Path to the original PDF file
        combined_text: Combined text from all pages extracted by Textract

    Returns:
        Path to the saved textract response file
    """
    pdf_dir = Path(pdf_path).parent
    pdf_stem = Path(pdf_path).stem
    textract_file = pdf_dir / f"{pdf_stem}_textract_response.txt"

    with open(textract_file, 'w', encoding='utf-8') as f:
        f.write(combined_text)

    logging.info(f"Textract response saved to: {textract_file}")
    return str(textract_file)


def load_textract_response(pdf_path: str) -> Optional[str]:
    """
    Load previously saved Textract response if it exists.

    Args:
        pdf_path: Path to the original PDF file

    Returns:
        Combined text if saved response exists, None otherwise
    """
    pdf_dir = Path(pdf_path).parent
    pdf_stem = Path(pdf_path).stem
    textract_file = pdf_dir / f"{pdf_stem}_textract_response.txt"

    if textract_file.exists():
        with open(textract_file, 'r', encoding='utf-8') as f:
            combined_text = f.read()
        logging.info(f"Loaded existing Textract response from: {textract_file}")
        return combined_text

    return None


def save_bedrock_response(pdf_path: str, bedrock_response: Dict, analysis_result: Dict) -> str:
    """
    Save complete Bedrock response object with tokens and metadata to classification output folder.

    Args:
        pdf_path: Path to the original PDF file
        bedrock_response: Complete response object from Bedrock API
        analysis_result: Extracted analysis result

    Returns:
        Path to the saved bedrock response file
    """
    output_dir = Path("data/output_data/classification")
    output_dir.mkdir(parents=True, exist_ok=True)

    pdf_stem = Path(pdf_path).stem
    bedrock_file = output_dir / f"{pdf_stem}_bedrock_response.json"

    # Combine all response data
    complete_response = {
        'source_file': pdf_path,
        'timestamp': datetime.now().isoformat(),
        'analysis_result': analysis_result,
        'bedrock_response': bedrock_response
    }

    with open(bedrock_file, 'w', encoding='utf-8') as f:
        json.dump(complete_response, f, indent=2, default=str)

    logging.info(f"Bedrock response saved to: {bedrock_file}")
    return str(bedrock_file)


def move_to_processed(pdf_path: str) -> str:
    """
    Move processed PDF file to 'processed' folder within the same directory.

    Args:
        pdf_path: Path to the PDF file to move

    Returns:
        New path of the moved file
    """
    pdf_file = Path(pdf_path)
    processed_dir = pdf_file.parent / "processed"
    processed_dir.mkdir(exist_ok=True)

    new_path = processed_dir / pdf_file.name

    # Handle case where file already exists in processed folder
    counter = 1
    while new_path.exists():
        stem = pdf_file.stem
        suffix = pdf_file.suffix
        new_path = processed_dir / f"{stem}_{counter}{suffix}"
        counter += 1

    shutil.move(str(pdf_file), str(new_path))
    logging.info(f"Moved processed file to: {new_path}")
    return str(new_path)


async def process_file_async(pdf_path: str) -> Dict:
    """
    Async wrapper for process_file that runs in a thread pool for true parallelism.

    Args:
        pdf_path: Path to the PDF file to process

    Returns:
        Dictionary containing processing results
    """
    loop = asyncio.get_event_loop()
    # Run the synchronous process_file function in a thread pool
    return await loop.run_in_executor(None, process_file, pdf_path)


def process_file(pdf_path: str) -> Dict:
    """
    Process a single PDF file with enhanced tracking and caching.

    Args:
        pdf_path: Path to the PDF file to process

    Returns:
        Dictionary containing processing results
    """
    # Validate input
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    if not pdf_path.lower().endswith('.pdf'):
        raise ValueError("Input file must be a PDF")

    # Setup logging
    logger = setup_logging()
    logger.info(f"Processing PDF: {pdf_path}")

    # Configuration
    BUCKET_NAME = "document-extraction-logistically"
    TEMP_PREFIX = "temp-pdf-processing"

    try:
        # Update tracking - started
        update_tracking_csv(pdf_path, "started")

        # Check for existing Textract response
        combined_text = load_textract_response(pdf_path)

        if combined_text:
            logger.info("Using cached Textract response")
        else:
            logger.info("Processing with Textract...")
            with tempfile.TemporaryDirectory() as temp_dir:
                logger.info("Splitting PDF into individual pages...")
                page_files = split_pdf(pdf_path, temp_dir)
                logger.info(f"Split PDF into {len(page_files)} pages")

                logger.info("Processing pages with Textract in parallel...")
                combined_text = process_pdf_pages_parallel(page_files, BUCKET_NAME, TEMP_PREFIX)

                # Validate and clean the extracted text
                combined_text = validate_and_clean_extracted_text(combined_text, pdf_path)

                # Save Textract response for future use
                save_textract_response(pdf_path, combined_text)

        logger.info("Analyzing document types with Bedrock...")
        analysis_result, bedrock_response = analyze_document_types_with_bedrock(combined_text)

        # Save Bedrock response with full metadata
        bedrock_response_path = save_bedrock_response(pdf_path, bedrock_response, analysis_result)

        # Update tracking - completed
        metadata = {
            'total_pages': len(analysis_result.get('documents', [])),
            'textract_response_saved': True,
            'bedrock_response_saved': True
        }
        update_tracking_csv(pdf_path, "completed", metadata)

        # Move file to processed folder
        new_path = move_to_processed(pdf_path)

        result = {
            'source_file': pdf_path,
            'processed_file_path': new_path,
            'analysis': analysis_result,
            'combined_text': combined_text,
            'bedrock_response_path': bedrock_response_path
        }

        logger.info(f"Successfully processed: {pdf_path}")
        return result

    except Exception as e:
        logger.error(f"Processing failed: {e}")
        update_tracking_csv(pdf_path, "failed", {'error': str(e)})
        raise


def process_folder(folder_path: str, max_files: Optional[int] = None) -> List[Dict]:
    """
    Process multiple PDF files in a folder.

    Args:
        folder_path: Path to the folder containing PDF files
        max_files: Maximum number of files to process (None for all files)

    Returns:
        List of processing results for each file
    """
    folder = Path(folder_path)
    if not folder.exists() or not folder.is_dir():
        raise ValueError(f"Folder not found or not a directory: {folder_path}")

    # Find all PDF files
    pdf_files = list(folder.glob("*.pdf"))

    if not pdf_files:
        logging.warning(f"No PDF files found in: {folder_path}")
        return []

    # Limit number of files if specified
    if max_files:
        pdf_files = pdf_files[:max_files]

     logger = setup_logging()
    logger.info(f"Found {len(pdf_files)} PDF files to process")

    results = []
    for i, pdf_file in enumerate(pdf_files, 1):
        logger.info(f"Processing file {i}/{len(pdf_files)}: {pdf_file.name}")
        try:
            result = process_file(str(pdf_file))
            results.append(result)
        except Exception as e:
            logger.error(f"Failed to process {pdf_file.name}: {e}")
            results.append({
                'source_file': str(pdf_file),
                'error': str(e),
                'status': 'failed'
            })

    logger.info(f"Completed processing {len(results)} files")
    return results


async def process_folder_async(folder_path: str, max_files: Optional[int] = None, max_concurrent: int = 3) -> List[Dict]:
    """
    Process multiple PDF files in a folder asynchronously for faster execution.

    Args:
        folder_path: Path to the folder containing PDF files
        max_files: Maximum number of files to process (None for all files)
        max_concurrent: Maximum number of files to process concurrently

    Returns:
        List of processing results for each file
    """
    folder = Path(folder_path)
    if not folder.exists() or not folder.is_dir():
        raise ValueError(f"Folder not found or not a directory: {folder_path}")

    # Find all PDF files
    pdf_files = list(folder.glob("*.pdf"))

    if not pdf_files:
        logging.warning(f"No PDF files found in: {folder_path}")
        return []

    # Limit number of files if specified
    if max_files:
        pdf_files = pdf_files[:max_files]

    logger = setup_logging()
    logger.info(f"Found {len(pdf_files)} PDF files to process asynchronously")

    # Create semaphore to limit concurrent processing
    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_with_semaphore(pdf_file: Path) -> Dict:
        async with semaphore:
            try:
                logger.info(f"Starting async processing: {pdf_file.name}")
                # Run the async process_file_async directly
                result = await process_file_async(str(pdf_file))
                return result
            except Exception as e:
                logger.error(f"Failed to process {pdf_file.name}: {e}")
                return {
                    'source_file': str(pdf_file),
                    'error': str(e),
                    'status': 'failed'
                }

    # Process all files concurrently
    tasks = [process_with_semaphore(pdf_file) for pdf_file in pdf_files]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Handle any exceptions that occurred
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Exception processing {pdf_files[i].name}: {result}")
            processed_results.append({
                'source_file': str(pdf_files[i]),
                'error': str(result),
                'status': 'failed'
            })
        else:
            processed_results.append(result)

    logger.info(f"Completed async processing {len(processed_results)} files")
    return processed_results


def main(pdf_path: str):
    """Process a PDF file with Textract and Bedrock. Callable from other Python files."""
    # Validate input
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    if not pdf_path.lower().endswith('.pdf'):
        raise ValueError("Input file must be a PDF")

    # Setup logging
    logger = setup_logging()
    logger.info(f"Processing PDF: {pdf_path}")

    # Configuration
    BUCKET_NAME = "document-extraction-logistically"  # Update with your bucket
    TEMP_PREFIX = "temp-pdf-processing"

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            logger.info("Splitting PDF into individual pages...")
            page_files = split_pdf(pdf_path, temp_dir)
            logger.info(f"Split PDF into {len(page_files)} pages")

            logger.info("Processing pages with Textract in parallel...")
            combined_text = process_pdf_pages_parallel(page_files, BUCKET_NAME, TEMP_PREFIX)

            # Validate and clean the extracted text
            combined_text = validate_and_clean_extracted_text(combined_text, pdf_path)

            logger.info("Analyzing document types with Bedrock...")
            analysis_result, bedrock_response = analyze_document_types_with_bedrock(combined_text)

            # Save Textract response for future use
            save_textract_response(pdf_path, combined_text)

            # Save Bedrock response with full metadata
            bedrock_response_path = save_bedrock_response(pdf_path, bedrock_response, analysis_result)

            print("\n" + "="*50)
            print("DOCUMENT TYPE ANALYSIS RESULTS")
            print("="*50)
            print(json.dumps(analysis_result, indent=2))

            # Legacy output file for backward compatibility
            output_file = f"{Path(pdf_path).stem}_analysis.json"
            with open(output_file, 'w') as f:
                json.dump({
                    'source_file': pdf_path,
                    'analysis': analysis_result,
                    'combined_text': combined_text
                }, f, indent=2)

            logger.info(f"Results saved to: {output_file}")
            logger.info(f"Bedrock response saved to: {bedrock_response_path}")

    except Exception as e:
        logger.error(f"Processing failed: {e}")
        raise


async def main_async(folder_path, max_files, max_concurrent):
    """Async main function demonstrating the new async capabilities."""

    # Example: Process multiple files from a folder asynchronously (limit to 3 files for demo)
    try:
        print("Starting async folder processing...")
        start_time = time.time()

        results = await process_folder_async(
            folder_path,
            max_files=max_files,
            max_concurrent=max_concurrent
        )

        end_time = time.time()
        print(f"Async folder processing completed in {end_time - start_time:.2f} seconds!")
        print(f"Processed {len(results)} files.")

        for result in results:
            if 'error' not in result:
                print(f"  ✓ {Path(result['source_file']).name}")
                # printing extracted data
                print(f"    - Analysis Result: ")
                pprint(json.dumps(result['analysis'], indent=4))

            else:
                print(f"  ✗ {Path(result['source_file']).name} - {result['error']}")

    except Exception as e:
        print(f"Async folder processing failed: {e}")


if __name__ == "__main__":

    from pprint import pprint
    import json

    # Process single file
    file_path = '/home/<USER>/Documents/repositories/logistically/data/input_data/classification/1-03132025TT.pdf'
    result = process_file(file_path)
    print("")
    pprint(f"{result['analysis']}")
    print(json.dumps(result['analysis'], indent=4))

    # Processing folder 
    # folder_path = '/home/<USER>/Documents/repositories/logistically/data/input_data/classification'
    # max_files = 15
    # max_concurrent = 4
    # asyncio.run(main_async(folder_path, max_files, max_concurrent))